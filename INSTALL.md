# Panduan Instalasi Sistem Absensi PDAM

## 📋 Persyaratan Sistem

### Software yang <PERSON><PERSON>lukan:
- **PHP 7.4+** dengan extension:
  - PDO
  - PDO_MySQL
  - JSON
  - Session
- **MySQL 5.7+** atau **MariaDB 10.2+**
- **Web Server**: Apache atau Nginx
- **XAMPP/WAMP/LAMP** (untuk development)

### Hardware Minimum:
- RAM: 512 MB
- Storage: 100 MB
- Processor: 1 GHz

## 🚀 Langkah Instalasi

### 1. Persiapan Environment

#### Untuk XAMPP (Windows):
```bash
# Download dan install XAMPP dari https://www.apachefriends.org/
# Start Apache dan MySQL dari XAMPP Control Panel
```

#### Untuk Ubuntu/Linux:
```bash
sudo apt update
sudo apt install apache2 mysql-server php php-mysql php-json
sudo systemctl start apache2
sudo systemctl start mysql
```

### 2. Setup Database

#### A. Buat Database
```sql
# Login ke MySQL
mysql -u root -p

# Buat database
CREATE DATABASE absensipdam CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# Buat user khusus (opsional, untuk keamanan)
CREATE USER 'absensipdam_user'@'localhost' IDENTIFIED BY 'password_anda';
GRANT ALL PRIVILEGES ON absensipdam.* TO 'absensipdam_user'@'localhost';
FLUSH PRIVILEGES;

# Keluar dari MySQL
EXIT;
```

#### B. Import Database Schema
```bash
# Import file SQL
mysql -u root -p absensipdam < database/absensipdam.sql

# Atau jika menggunakan user khusus
mysql -u absensipdam_user -p absensipdam < database/absensipdam.sql
```

### 3. Konfigurasi Aplikasi

#### A. Copy Files
```bash
# Copy semua file ke folder web server
# Untuk XAMPP Windows: C:\xampp\htdocs\absensipdam\
# Untuk Linux: /var/www/html/absensipdam/
```

#### B. Set Permissions (Linux/Mac)
```bash
sudo chown -R www-data:www-data /var/www/html/absensipdam/
sudo chmod -R 755 /var/www/html/absensipdam/
sudo chmod -R 777 /var/www/html/absensipdam/uploads/
```

#### C. Konfigurasi Database
Edit file `config/database.php`:
```php
<?php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');  // atau 'absensipdam_user'
define('DB_PASSWORD', '');      // password MySQL Anda
define('DB_NAME', 'absensipdam');
define('DB_CHARSET', 'utf8mb4');
?>
```

### 4. Test Instalasi

#### A. Test Koneksi Database
Buka browser dan akses:
```
http://localhost/absensipdam/test_connection.php
```

Pastikan semua test menunjukkan status ✓ (berhasil).

#### B. Test Login Admin
```
URL: http://localhost/absensipdam/login.php
Username: admin
Password: password
```

### 5. Konfigurasi Lanjutan

#### A. Ubah Password Admin Default
1. Login sebagai admin
2. Buka database dan update password:
```sql
UPDATE admin_users 
SET password = '$2y$10$hash_password_baru' 
WHERE username = 'admin';
```

#### B. Konfigurasi Timezone
Edit `config/config.php`:
```php
date_default_timezone_set('Asia/Jakarta'); // Sesuaikan timezone
```

#### C. Konfigurasi Email (Opsional)
Untuk notifikasi email, edit `config/config.php`:
```php
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
```

## 📱 Setup API untuk Mobile App

### 1. Test API Endpoints
```bash
# Test authentication
curl -X POST http://localhost/absensipdam/api/auth.php?action=verify \
  -H "Content-Type: application/json" \
  -d '{"nik":"12345"}'

# Test locations
curl http://localhost/absensipdam/api/attendance.php?action=locations
```

### 2. Konfigurasi CORS (jika diperlukan)
Edit `api/config.php` untuk mengatur domain yang diizinkan:
```php
$allowedOrigins = [
    'http://localhost:3000',
    'https://yourdomain.com'
];
```

## 🔧 Troubleshooting

### Problem: Database Connection Error
**Solusi:**
1. Pastikan MySQL service berjalan
2. Cek kredensial di `config/database.php`
3. Pastikan database `absensipdam` sudah dibuat
4. Cek firewall settings

### Problem: Permission Denied
**Solusi (Linux):**
```bash
sudo chown -R www-data:www-data /var/www/html/absensipdam/
sudo chmod -R 755 /var/www/html/absensipdam/
```

### Problem: Session Issues
**Solusi:**
1. Pastikan folder session PHP writable
2. Cek PHP session configuration
3. Clear browser cookies

### Problem: API CORS Error
**Solusi:**
1. Update `api/config.php` dengan domain yang benar
2. Pastikan header CORS sudah diset
3. Cek `.htaccess` configuration

## 📊 Data Default

### Admin User:
- **Username:** admin
- **Password:** password
- **Role:** Super Admin

### Sample Data:
- 3 Departemen (IT, HRD, Operasional)
- 5 Jabatan (Manager, Supervisor, Staff, dll)
- 3 Jadwal Kerja (Pagi, Siang, Malam)
- 2 Lokasi Absen (Kantor Pusat, Cabang)
- 10 Karyawan sample

## 🔐 Keamanan

### Rekomendasi Keamanan:
1. **Ubah password admin default**
2. **Gunakan HTTPS di production**
3. **Set proper file permissions**
4. **Regular backup database**
5. **Update PHP dan MySQL secara berkala**
6. **Monitor log files**

### File Permissions:
```
Folders: 755
PHP Files: 644
Config Files: 600
Upload Folder: 777
```

## 📈 Performance Optimization

### Database:
```sql
-- Add indexes untuk performance
ALTER TABLE attendance ADD INDEX idx_employee_date (employee_id, attendance_date);
ALTER TABLE employee_schedules ADD INDEX idx_employee_active (employee_id, is_active);
```

### PHP Configuration:
```ini
; php.ini optimizations
memory_limit = 256M
max_execution_time = 300
upload_max_filesize = 10M
post_max_size = 10M
```

### Apache Configuration:
```apache
# Enable compression
LoadModule deflate_module modules/mod_deflate.so
<Location />
    SetOutputFilter DEFLATE
</Location>
```

## 🔄 Backup & Restore

### Backup Database:
```bash
mysqldump -u root -p absensipdam > backup_$(date +%Y%m%d).sql
```

### Restore Database:
```bash
mysql -u root -p absensipdam < backup_20241201.sql
```

### Backup Files:
```bash
tar -czf absensipdam_backup_$(date +%Y%m%d).tar.gz /var/www/html/absensipdam/
```

## 📞 Support

Jika mengalami masalah:
1. Cek file `test_connection.php` untuk diagnosis
2. Review log files di `/var/log/apache2/error.log`
3. Cek PHP error log
4. Pastikan semua requirements terpenuhi

---

**Sistem Absensi PDAM v1.0.0**  
© 2024 - Developed for PDAM
