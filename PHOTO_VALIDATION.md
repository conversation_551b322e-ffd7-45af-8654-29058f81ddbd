# Fitur Validasi Foto Absensi

## 📸 Overview

Sistem absensi PDAM telah dilengkapi dengan fitur validasi foto untuk memastikan keaslian absensi karyawan. Setiap karyawan wajib mengambil foto selfie saat melakukan absen masuk dan absen pulang.

## 🎯 Tujuan Fitur Foto

1. **Validasi Identitas**: Memastikan yang melakukan absensi adalah karyawan yang bersangkutan
2. **Mencegah Kecurangan**: Menghindari absensi titip atau proxy attendance
3. **Audit Trail**: Menyediakan bukti visual untuk keperluan audit
4. **Monitoring Real-time**: Admin dapat melihat foto absensi secara langsung

## 📱 Cara Kerja

### Untuk Karyawan (Mobile App):
1. **Absen Masuk**:
   - Buka aplikasi mobile
   - Pilih "Absen Masuk"
   - Ambil foto selfie
   - Pastikan lokasi GPS aktif
   - Tekan tombol "Absen Masuk"

2. **Absen Pulang**:
   - Buka aplikasi mobile
   - <PERSON><PERSON><PERSON> "Absen Pulang"
   - Ambil foto selfie
   - Pastikan lokasi GPS aktif
   - Tekan tombol "Absen Pulang"

### Untuk Admin (Web Dashboard):
1. **Monitoring Real-time**:
   - Buka halaman "Monitoring Absensi"
   - Lihat kolom "Foto" untuk setiap karyawan
   - Klik tombol "Lihat Foto" untuk melihat foto absen masuk/pulang

2. **Detail Absensi**:
   - Klik tombol "Detail" pada data absensi
   - Lihat foto absen masuk dan pulang dalam modal
   - Klik foto untuk memperbesar

## 🔧 Spesifikasi Teknis

### Format Foto:
- **Format**: JPEG, PNG
- **Ukuran Maksimal**: 5 MB
- **Encoding**: Base64 (untuk API)
- **Resolusi**: Otomatis disesuaikan

### Penyimpanan:
- **Lokasi**: `/uploads/attendance_photos/`
- **Naming Convention**: `YYYYMMDDHHMMSS_[checkin|checkout]_emp[ID].jpg`
- **Contoh**: `20241201143022_checkin_emp1.jpg`

### Keamanan:
- Folder uploads dilindungi dengan `.htaccess`
- Hanya file gambar yang diizinkan
- Tidak ada eksekusi script di folder uploads
- Validasi ukuran dan format file

## 📊 Database Schema

### Tabel `attendance`:
```sql
ALTER TABLE attendance 
ADD COLUMN check_in_photo VARCHAR(255) NULL,
ADD COLUMN check_out_photo VARCHAR(255) NULL;
```

### Tabel `attendance_photo_logs` (Optional):
```sql
CREATE TABLE attendance_photo_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    attendance_id INT NOT NULL,
    photo_type ENUM('check_in', 'check_out') NOT NULL,
    original_filename VARCHAR(255),
    stored_filename VARCHAR(255),
    file_size INT,
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    uploaded_by VARCHAR(50),
    ip_address VARCHAR(45),
    FOREIGN KEY (attendance_id) REFERENCES attendance(id)
);
```

## 🔌 API Integration

### Check-in dengan Foto:
```json
POST /api/attendance.php?action=checkin
{
    "nik": "123456789",
    "latitude": -7.2575,
    "longitude": 112.7521,
    "location_id": 1,
    "photo": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."
}
```

### Check-out dengan Foto:
```json
POST /api/attendance.php?action=checkout
{
    "nik": "123456789",
    "latitude": -7.2575,
    "longitude": 112.7521,
    "photo": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."
}
```

## 🖥️ Interface Admin

### Halaman Monitoring:
- **Kolom Foto**: Menampilkan status foto untuk setiap karyawan
- **Button "Lihat Foto"**: Untuk melihat foto absen masuk/pulang
- **Status Indikator**:
  - ✅ Hijau: Ada foto absen masuk dan pulang
  - 🔵 Biru: Hanya ada foto absen masuk
  - ⚠️ Kuning: Belum absen pulang
  - ❌ Abu-abu: Tidak ada foto

### Modal Detail:
- **Foto Absen Masuk**: Card biru dengan foto selfie saat masuk
- **Foto Absen Pulang**: Card hijau dengan foto selfie saat pulang
- **Klik untuk Zoom**: Foto dapat diklik untuk memperbesar
- **Download**: Tombol download untuk menyimpan foto

## 📋 Aturan dan Kebijakan

### Wajib Foto:
- Foto **WAJIB** diambil saat absen masuk dan pulang
- Foto harus menampakkan wajah dengan jelas
- Tidak boleh menggunakan foto lama atau foto orang lain

### Kualitas Foto:
- Pencahayaan cukup (tidak terlalu gelap/terang)
- Wajah terlihat jelas dan tidak tertutup
- Tidak boleh menggunakan filter atau edit foto

### Sanksi:
- Absensi tanpa foto akan ditandai untuk review
- Foto yang tidak sesuai akan diverifikasi manual
- Pelanggaran berulang akan dikenakan sanksi sesuai aturan perusahaan

## 🔍 Monitoring dan Audit

### Laporan Foto:
- Admin dapat melihat semua foto absensi
- Filter berdasarkan tanggal, karyawan, atau departemen
- Export data termasuk foto untuk audit

### Verifikasi:
- Sistem otomatis menyimpan metadata foto
- Timestamp dan lokasi GPS terekam
- IP address dan device info tersimpan

## 🚀 Implementasi

### Langkah Update Database:
```sql
-- Jalankan script update
SOURCE database/update_add_photos.sql;
```

### Setup Folder:
```bash
# Buat folder dan set permission
mkdir -p uploads/attendance_photos
chmod 755 uploads/attendance_photos
```

### Test API:
```bash
# Test dengan foto
curl -X POST http://localhost/absensipdam/api/attendance.php?action=checkin \
  -H "Content-Type: application/json" \
  -d '{
    "nik":"123456789",
    "latitude":-7.2575,
    "longitude":112.7521,
    "photo":"data:image/jpeg;base64,..."
  }'
```

## 📱 Mobile App Integration

### Camera Permission:
```javascript
// Request camera permission
navigator.mediaDevices.getUserMedia({ video: true })
```

### Capture Photo:
```javascript
// Capture photo from camera
const canvas = document.createElement('canvas');
const context = canvas.getContext('2d');
context.drawImage(video, 0, 0, canvas.width, canvas.height);
const photoData = canvas.toDataURL('image/jpeg', 0.8);
```

### Send to API:
```javascript
// Send photo with attendance data
const attendanceData = {
    nik: employee.nik,
    latitude: position.coords.latitude,
    longitude: position.coords.longitude,
    photo: photoData
};

fetch('/api/attendance.php?action=checkin', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(attendanceData)
});
```

## 🔧 Troubleshooting

### Masalah Umum:

1. **Foto tidak tersimpan**:
   - Cek permission folder uploads
   - Pastikan ukuran foto < 5MB
   - Validasi format base64

2. **Foto tidak tampil**:
   - Cek path file di database
   - Pastikan file exists di server
   - Cek .htaccess configuration

3. **Upload gagal**:
   - Cek PHP upload limits
   - Validasi format gambar
   - Cek disk space server

### Log Files:
- PHP error log: `/var/log/php_errors.log`
- Apache error log: `/var/log/apache2/error.log`
- Application log: Database table `activity_logs`

---

**Sistem Absensi PDAM v1.0.0**  
Fitur Validasi Foto - Memastikan Keaslian Absensi Karyawan
