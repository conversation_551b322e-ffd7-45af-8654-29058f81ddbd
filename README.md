# Sistem Absensi PDAM

Aplikasi web untuk mengelola absensi karyawan PDAM dengan fitur-fitur canggih termasuk rolling jadwal, shift lintas hari, dan API untuk aplikasi mobile.

## 🚀 Fitur Utama

### 1. **Manaj<PERSON><PERSON>**
- CRUD data karyawan lengkap (NIK, nama, departemen, jabatan, dll)
- Pengelompokan berdasarkan departemen dan jabatan
- Status aktif/non-aktif karyawan

### 2. **Manajemen Jam Kerja**
- Pengaturan multiple shift kerja
- Batasan waktu absen masuk dan keluar
- Support shift lintas hari (shift malam)
- Konfigurasi toleransi keterlambatan

### 3. **Manajemen Lokasi Absensi**
- Multiple lokasi absensi dengan koordinat GPS
- Pengaturan radius untuk setiap lokasi
- Validasi lokasi saat absensi

### 4. **Rolling Jadwal (Pertukaran Shift)**
- Sistem pertukaran jadwal antar karyawan
- Workflow approval oleh admin
- Riwayat pertukaran jadwal

### 5. **Monitoring Real-time**
- Dashboard monitoring absensi harian
- Filter berdasarkan departemen dan status
- Statistik kehadiran real-time
- Auto-refresh data

### 6. **Laporan Komprehensif**
- Laporan absensi bulanan
- Export ke Excel
- Print-friendly format
- Filter multi-parameter

### 7. **API untuk Mobile App**
- RESTful API lengkap
- Autentikasi berbasis NIK
- Endpoint untuk absensi, jadwal, dan laporan
- Rate limiting dan security

### 8. **Shift Lintas Hari**
- Penanganan khusus untuk shift malam
- Tanggal absensi mengikuti tanggal mulai shift
- Logika check-in/out yang akurat

### 9. **Validasi Foto Absensi**
- Foto wajib untuk absen masuk dan pulang
- Upload foto selfie untuk validasi identitas
- Penyimpanan aman dengan enkripsi nama file
- Preview foto di dashboard monitoring
- Download foto untuk keperluan audit

## 🛠️ Teknologi

- **Backend**: PHP Native (tanpa framework)
- **Database**: MySQL
- **Frontend**: Bootstrap 5, jQuery, DataTables
- **Charts**: Chart.js
- **Icons**: Font Awesome
- **Maps**: Google Maps integration

## 📋 Persyaratan Sistem

- PHP 7.4 atau lebih tinggi
- MySQL 5.7 atau lebih tinggi
- Apache/Nginx web server
- Extension PHP: PDO, PDO_MySQL, JSON

## 🚀 Instalasi

### 1. Clone atau Download
```bash
git clone [repository-url]
# atau download dan extract ke folder web server
```

### 2. Setup Database
```bash
# Import database
mysql -u root -p < database/absensipdam.sql

# Atau buat database manual dan import
mysql -u root -p
CREATE DATABASE absensipdam;
USE absensipdam;
SOURCE database/absensipdam.sql;
```

### 3. Konfigurasi Database
Edit file `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', 'your_password');
define('DB_NAME', 'absensipdam');
```

### 4. Set Permissions
```bash
chmod 755 uploads/
chmod 644 config/*.php
```

### 5. Akses Aplikasi
- URL: `http://localhost/absensipdam`
- Login Admin:
  - Username: `admin`
  - Password: `password` (hash: bcrypt)

## 📱 API Documentation

API tersedia di `/api/` dengan endpoint berikut:

### Authentication
- `POST /api/auth.php?action=login` - Login karyawan
- `POST /api/auth.php?action=verify` - Verifikasi NIK
- `GET /api/auth.php?action=profile` - Profil karyawan

### Attendance
- `POST /api/attendance.php?action=checkin` - Absen masuk
- `POST /api/attendance.php?action=checkout` - Absen keluar
- `GET /api/attendance.php?action=history` - Riwayat absensi
- `GET /api/attendance.php?action=today` - Absensi hari ini
- `GET /api/attendance.php?action=locations` - Lokasi absensi

### Schedule
- `GET /api/schedule.php?action=current` - Jadwal saat ini
- `GET /api/schedule.php?action=monthly` - Jadwal bulanan
- `GET /api/schedule.php?action=swaps` - Pertukaran jadwal
- `POST /api/schedule.php?action=request_swap` - Request pertukaran

Dokumentasi lengkap: [API README](api/README.md)

## 🗂️ Struktur Folder

```
absensipdam/
├── api/                    # API endpoints
│   ├── auth.php           # Authentication API
│   ├── attendance.php     # Attendance API
│   ├── schedule.php       # Schedule API
│   ├── config.php         # API configuration
│   └── README.md          # API documentation
├── config/                # Configuration files
│   ├── config.php         # Main configuration
│   └── database.php       # Database configuration
├── database/              # Database files
│   └── absensipdam.sql    # Database schema
├── includes/              # Include files
│   ├── header.php         # Header template
│   ├── footer.php         # Footer template
│   └── attendance_helper.php # Attendance logic
├── uploads/               # Upload directory
├── employees.php          # Employee management
├── schedules.php          # Schedule management
├── locations.php          # Location management
├── schedule_swaps.php     # Schedule swap management
├── monitoring.php         # Attendance monitoring
├── reports.php            # Reports
├── login.php              # Login page
├── logout.php             # Logout handler
├── index.php              # Dashboard
├── .htaccess              # Apache configuration
└── README.md              # This file
```

## 🔧 Konfigurasi

### Timezone
Default timezone: `Asia/Jakarta`
Ubah di `config/config.php`:
```php
date_default_timezone_set('Asia/Jakarta');
```

### Session Timeout
Default: 1 jam
Ubah di `config/config.php`:
```php
define('SESSION_TIMEOUT', 3600);
```

### Rate Limiting API
Default: 1000 request/jam per IP
Ubah di `api/config.php`:
```php
checkRateLimit($clientIp, 1000, 3600);
```

## 🔐 Keamanan

- Password hashing menggunakan bcrypt
- CSRF protection
- SQL injection prevention dengan prepared statements
- XSS protection dengan input sanitization
- Session management yang aman
- Rate limiting untuk API
- CORS configuration untuk mobile app

## 📊 Database Schema

### Tabel Utama:
- `admin_users` - Data admin
- `employees` - Data karyawan
- `departments` - Departemen
- `positions` - Jabatan
- `work_schedules` - Template jam kerja
- `employee_schedules` - Assignment jadwal ke karyawan
- `locations` - Lokasi absensi
- `attendance` - Data absensi
- `schedule_swaps` - Pertukaran jadwal

### Tabel Log:
- `activity_logs` - Log aktivitas admin
- `api_logs` - Log aktivitas API
- `rate_limits` - Rate limiting data

## 🚀 Deployment

### Production Setup:
1. Set `display_errors = Off` di PHP
2. Enable HTTPS
3. Set proper file permissions
4. Configure backup database
5. Set up monitoring
6. Configure firewall

### Performance:
- Enable gzip compression
- Set cache headers
- Optimize database queries
- Use CDN untuk assets

## 🐛 Troubleshooting

### Database Connection Error:
- Periksa kredensial database di `config/database.php`
- Pastikan MySQL service berjalan
- Periksa firewall settings

### Session Issues:
- Periksa PHP session configuration
- Pastikan folder session writable
- Periksa session timeout settings

### API Issues:
- Periksa CORS headers
- Validasi JSON format
- Periksa rate limiting
- Review API logs

## 📝 Changelog

### Version 1.0.0
- Initial release
- Complete admin panel
- Mobile API
- Cross-day shift support
- Schedule swapping
- Real-time monitoring
- Comprehensive reporting

## 🤝 Contributing

1. Fork repository
2. Create feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

## 📄 License

This project is licensed under the MIT License.

## 👥 Support

Untuk support dan pertanyaan:
- Email: <EMAIL>
- Documentation: [Link to docs]
- Issues: [GitHub Issues]

---

**Sistem Absensi PDAM v1.0.0**  
Developed with ❤️ for PDAM
