Saya ingin mmembuat aplikasi untuk admin absensi karyawan pdam khususnya untuk karyawan yang memiliki hak istimewa dimana ada pertauran jam kerja yang akan saya jelaskan dibawah ini.

Fitur yang saya inginkan
1. <PERSON><PERSON> karyawan untuk menambah. menghapus dan mengedit data karyawan. dalam menu karyawan ini memiliki beberapa field yang penting seperti nik, nama, bidang, jabatan, jam kerja dll
2. Menu Jam kerja dimana nanti menu ini berisikan jam kerja karyawan, batasan absen masuk sebelum dan sesudah contohnya ketika jam masukny jam 07:00 maka tidak bisa absen sebelum jam 07:00 dan ketika batas absen masuknya sampai jam 08:00 maka diatas jam 08:00 tidak bisa absen.
3. menu lokasi. Menu ini berfungsi sebagai titik lokasi karyawan yang didalam menu ini terdapat fitur tambah data lokasi, edit dan hapus. field yang dibutuhkan adalah nama lokasi, longitude latitude dan radius absen.
4. Menu monitoring absensi dimana menu ini berfungsi untuk memantau absen karyawan.
5. menu laporan absen untuk melihat laporan absen dalam jangan waktu sebulan.

Fitur yang diharapakan dapat terealisasi.
1. Beberapa karyawan memiliki jam kerja yang mengharuskan rolling dengan karyawan lain sehingga saya ingin jam kerja dapat flexibel. contoh kasusnya karyawan A pada jadwal absen yang dibuat kantor adalah dari tanggal 01 sampai tanggal 06 masuk pagi di jam 07:00 dan pulang di jam 15:00 dan karyawan B masuk jam 15:00 dan pulang 22:00. pada suatu hari karyawan A ini ingin rolling jadwal jam kerja dengan si B maka saya ingin ada fitur yang dapat merolling jadwal jam kerja karyawan tersebut.
2. Saya ingin juga ada fitur ketika karyawan memiliki jadwal lintas hari. contohnya ketika karyawan memiliki jam kerja di jam masuk 22:00 pada tanggal 01 dan pulang 07:00 pada tanggal 02. maka data absensi terhitung tetap tanggal 1.

Rencana pembuatan.
1. Buatkan terlebih dahulu menu karyawan dilanjut menu - menu lainnya sesuai aturan dan berurutan agar tidak ada eror yang tidak di harapkan.
2. Amankan juga menu admin ini agar tidak dapat diakses oleh karyawan sehingga tidak ada perubahan data yang dilakukan karaywan dengan membuat halaman loginnya.
3. Buatkan API yang nantinya akan di hubungkan ke aplikasi android.

Teknologi yang dipakai
1. tanpa framework alias PHP native
2. MySQL database
3. Aplikasi berbasis web.