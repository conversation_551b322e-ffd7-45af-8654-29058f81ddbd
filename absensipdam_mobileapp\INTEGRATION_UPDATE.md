# Update Integrasi dengan API PDAM

## 📱 Overview

Aplikasi mobile Ionic React yang sudah ada telah diupdate untuk terintegrasi dengan sistem absensi PDAM yang baru. Semua halaman utama (Home, Absensi, Histori) tetap menggunakan komponen yang sudah ada, hanya disesuaikan untuk menggunakan API PDAM.

## 🔄 Perubahan yang Dilakukan

### 1. **Halaman Home** (`src/pages/Home.tsx`)
**Perubahan:**
- ✅ **Import API Service**: Menambahkan `AttendanceService` dan `StorageService`
- ✅ **Import Utilities**: Menambahkan semua utility functions untuk data PDAM
- ✅ **Update fetchAbsensiHariIni()**: Menggunakan `AttendanceService.getToday()`
- ✅ **Data Conversion**: Convert response PDAM API ke format yang diharapkan aplikasi
- ✅ **User Profile Integration**: Menggunakan data profile dari PDAM API
- ✅ **Load PDAM Data**: Fungsi `loadPDAMData()` untuk load semua data terkait
- ✅ **UI Updates**: Header menampilkan nama, posisi, dan department dari PDAM
- ✅ **Backward Compatibility**: Tetap support offline data dan localStorage

**API Integration:**
```typescript
// Sebelum (API lama)
const response = await fetch(`https://absensiku.trunois.my.id/api/monitoring_presensi.php...`);

// Sesudah (API PDAM)
const response = await AttendanceService.getToday(nik);
```

**Data Mapping:**
```typescript
// Convert PDAM response ke format aplikasi
onlineData = {
  id: pdamData.id,
  user_id: pdamData.employee_id,
  tanggal: pdamData.attendance_date,
  jam_masuk: pdamData.check_in_time ? new Date(pdamData.check_in_time).toLocaleTimeString('id-ID') : null,
  jam_pulang: pdamData.check_out_time ? new Date(pdamData.check_out_time).toLocaleTimeString('id-ID') : null,
  status: pdamData.status === 'present' ? 'Hadir' : 'Terlambat',
  // ... mapping lainnya
};
```

### 2. **Halaman Absensi** (`src/pages/Absensi.tsx`)
**Perubahan:**
- ✅ **Import API Service**: Menambahkan `AttendanceService`
- ✅ **Update handleValidasi()**: Menggunakan `AttendanceService.checkIn()` dan `checkOut()`
- ✅ **Update checkAbsensiHariIni()**: Menggunakan API PDAM untuk cek status
- ✅ **Photo Integration**: Foto langsung dikirim dalam base64 format
- ✅ **GPS Integration**: Koordinat GPS dikirim ke API PDAM

**API Integration:**
```typescript
// Check-in dengan foto
const response = await AttendanceService.checkIn({
  nik: nik,
  latitude: currentLocation.lat,
  longitude: currentLocation.lng,
  photo: photo // base64 photo
});

// Check-out dengan foto  
const response = await AttendanceService.checkOut({
  nik: nik,
  latitude: currentLocation.lat,
  longitude: currentLocation.lng,
  photo: photo // base64 photo
});
```

**Status Checking:**
```typescript
// Cek status absensi hari ini
const response = await AttendanceService.getToday(nik);
if (response.success && response.data) {
  const absensiHariIni = response.data;
  if (absensiHariIni.check_in_time && !absensiHariIni.check_out_time) {
    setJenisAbsensi('pulang'); // Sudah masuk, belum pulang
  }
}
```

### 3. **Halaman Histori** (`src/pages/Histori.tsx`)
**Perubahan:**
- ✅ **Import API Service**: Menambahkan `AttendanceService` dan `StorageService`
- ✅ **Update fetchAbsensiData()**: Menggunakan `AttendanceService.getHistory()`
- ✅ **Update getFotoUrl()**: Path foto disesuaikan dengan PDAM
- ✅ **Data Conversion**: Convert response PDAM ke format aplikasi
- ✅ **User Management**: Menggunakan `StorageService.getUser()`

**API Integration:**
```typescript
// Ambil histori 3 bulan terakhir
const response = await AttendanceService.getHistory(user.nik, startDate, endDate, 100);

// Convert data ke format aplikasi
const convertedData = response.data.map((item: any) => ({
  id: item.id.toString(),
  user_id: item.employee_id.toString(),
  tanggal: item.attendance_date,
  jam_masuk: item.check_in_time ? new Date(item.check_in_time).toLocaleTimeString('id-ID') : null,
  jam_pulang: item.check_out_time ? new Date(item.check_out_time).toLocaleTimeString('id-ID') : null,
  foto_masuk: item.check_in_photo,
  foto_pulang: item.check_out_photo,
  status: item.status === 'present' ? 'Hadir' : 'Terlambat'
}));
```

**Photo URL Update:**
```typescript
// Path foto PDAM
const getFotoUrl = (fotoName: string | null) => {
  if (!fotoName) return null;
  return `https://absensipdam.trunois.my.id/uploads/attendance_photos/${fotoName}`;
};
```

### 4. **Halaman Login** (`src/pages/Login.tsx`)
**Perubahan:**
- ✅ **Simplified Login**: Login hanya dengan NIK (tanpa password)
- ✅ **API Integration**: Menggunakan `AuthService.login()` dan `verify()`
- ✅ **Storage Management**: Menggunakan `StorageService`

### 5. **App Configuration** (`src/App.tsx`)
**Perubahan:**
- ✅ **Route Priority**: Halaman lama menjadi default, halaman baru sebagai alternatif
- ✅ **Storage Service**: Menggunakan `StorageService` untuk authentication
- ✅ **Import Updates**: Menambahkan halaman testing

## 🔧 Fitur yang Tetap Berfungsi

### 1. **Offline Support**
- ✅ **Offline Queue**: Data absensi tetap disimpan offline jika tidak ada koneksi
- ✅ **Auto Sync**: Data offline akan sync otomatis saat online
- ✅ **Backup Data**: Data backup tetap tersimpan di localStorage

### 2. **Photo Validation**
- ✅ **Camera Integration**: Foto tetap diambil menggunakan camera
- ✅ **Base64 Encoding**: Foto di-encode base64 sebelum dikirim
- ✅ **Photo Display**: Foto tetap bisa dilihat di histori

### 3. **GPS Tracking**
- ✅ **Location Validation**: GPS coordinates tetap divalidasi
- ✅ **Radius Checking**: Validasi radius lokasi kerja
- ✅ **Offline Location**: Support offline location caching

### 4. **UI/UX**
- ✅ **Existing Design**: Semua design dan layout tetap sama
- ✅ **Navigation**: Navigasi antar halaman tetap sama
- ✅ **User Experience**: Flow aplikasi tidak berubah

## 📊 Data Mapping

### PDAM API → Aplikasi Format

| PDAM Field | App Field | Conversion |
|------------|-----------|------------|
| `id` | `id` | `toString()` |
| `employee_id` | `user_id` | `toString()` |
| `attendance_date` | `tanggal` | Direct |
| `check_in_time` | `jam_masuk` | `toLocaleTimeString('id-ID')` |
| `check_out_time` | `jam_pulang` | `toLocaleTimeString('id-ID')` |
| `check_in_photo` | `foto_masuk` | Direct |
| `check_out_photo` | `foto_pulang` | Direct |
| `check_in_latitude,longitude` | `lokasi_masuk` | `"lat,lng"` |
| `check_out_latitude,longitude` | `lokasi_pulang` | `"lat,lng"` |
| `status` | `status` | `present→Hadir, late→Terlambat` |
| `notes` | `keterangan` | Direct |

## 🚀 Testing

### 1. **Functional Testing**
```bash
# Test login dengan NIK
# Test absensi masuk dengan foto
# Test absensi pulang dengan foto  
# Test lihat histori absensi
# Test offline functionality
```

### 2. **API Testing**
- ✅ **Built-in API Test**: Halaman `/api-test` untuk testing endpoint
- ✅ **Health Check**: Cek koneksi server real-time
- ✅ **Error Handling**: Test error scenarios

### 3. **Compatibility Testing**
- ✅ **Backward Compatibility**: Aplikasi tetap bisa handle data lama
- ✅ **Migration**: Data existing tetap bisa diakses
- ✅ **Fallback**: Fallback ke data offline jika API error

## 📱 User Experience

### 1. **Login Flow**
```
1. User input NIK
2. App verify NIK dengan API PDAM
3. Jika valid → redirect ke dashboard
4. Jika invalid → tampilkan error
```

### 2. **Absensi Flow**
```
1. User buka halaman absensi
2. App cek status absensi hari ini
3. User ambil foto selfie
4. App validasi GPS location
5. User tekan tombol absen
6. App kirim data ke API PDAM
7. Tampilkan konfirmasi berhasil
```

### 3. **Histori Flow**
```
1. User buka halaman histori
2. App load data dari API PDAM
3. User bisa filter berdasarkan bulan/status
4. User bisa lihat detail dan foto absensi
```

## 🔧 Configuration

### API Base URL
File: `src/config/api.ts`
```typescript
export const API_BASE_URL = 'https://absensipdam.trunois.my.id/api';
```

### Photo Path
```typescript
const photoUrl = `https://absensipdam.trunois.my.id/uploads/attendance_photos/${filename}`;
```

## 📞 Support

### Troubleshooting
1. **Login Gagal**: Cek NIK di database PDAM
2. **Absensi Gagal**: Cek koneksi internet dan GPS
3. **Foto Tidak Muncul**: Cek path foto di server
4. **Data Tidak Sync**: Cek API endpoint dan response

### Testing Tools
- **API Test Page**: `/api-test` - Test semua endpoint
- **Health Check**: Cek status server real-time
- **Debug Console**: `debugStatusAbsen()` untuk debug offline data

---

**Update Integrasi PDAM v1.0.0**  
Semua halaman existing berhasil diintegrasikan dengan API PDAM tanpa mengubah UI/UX yang sudah ada.
