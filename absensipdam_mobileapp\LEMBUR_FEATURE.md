# Fitur Lembur - Aplikasi Absensi PDAM

## Deskripsi
Fitur lembur memungkinkan karyawan untuk mencatat waktu lembur mereka dengan sistem validasi foto untuk memastikan kehadiran yang akurat.

## Konsep Kerja

### 1. <PERSON><PERSON> Lembur
Karyawan mengisi form dengan data berikut:
- **Tanggal Lembur**: Otomatis mengikuti tanggal hari ini
- **Keterangan**: Deskripsi pekerjaan yang akan dilakukan saat lembur
- **Jam Mulai**: Waktu mulai lembur (default: waktu saat ini)
- **Foto Mulai**: Foto selfie sebagai validasi kehadiran

### 2. Selesai Lembur
Setelah lembur selesai, karyawan mengisi:
- **Jam Selesai**: Waktu selesai lembur (default: waktu saat ini)
- **Foto Selesai**: Foto selfie sebagai validasi bahwa lembur benar-benar dilakukan sampai selesai

## Fitur Utama

### ✅ Form Mulai Lembur
- Input keterangan lembur (wajib)
- Input jam mulai (default: waktu saat ini)
- Kamera untuk foto mulai (wajib)
- Validasi form sebelum submit

### ✅ Status Lembur Aktif
- Menampilkan informasi lembur yang sedang berlangsung
- Menampilkan durasi lembur real-time
- Card khusus dengan animasi untuk lembur aktif

### ✅ Form Selesai Lembur
- Input jam selesai (default: waktu saat ini)
- Kamera untuk foto selesai (wajib)
- Tombol selesai lembur

### ✅ Riwayat Lembur
- Menampilkan daftar lembur yang sudah selesai
- Informasi lengkap: tanggal, jam mulai/selesai, durasi, keterangan
- Badge status untuk setiap riwayat

### ✅ Kamera Terintegrasi
- Modal kamera dengan preview real-time
- Capture foto langsung dari browser
- Preview foto sebelum submit
- Opsi untuk mengambil ulang foto

## API Backend

### Endpoint: `https://absensiku.trunois.my.id/api/lembur.php`

#### Headers Required:
```
x-api-key: absensiku_api_key_2023
Content-Type: application/json
```

#### 1. POST - Mulai Lembur
```json
{
  "nama_karyawan": "string",
  "keterangan": "string",
  "tanggal_lembur": "YYYY-MM-DD",
  "jam_mulai": "HH:MM",
  "foto_mulai": "base64_string",
  "status": "Menunggu"
}
```

#### 2. PUT - Selesai Lembur
```json
{
  "id": "string",
  "jam_selesai": "HH:MM",
  "foto_selesai": "base64_string",
  "status": "Selesai"
}
```

#### 3. GET - Ambil Data Lembur
Mengembalikan array data lembur untuk semua karyawan.

## Struktur Database
Tabel `lembur` dengan kolom:
- `id` (Primary Key)
- `nama_karyawan` (VARCHAR)
- `keterangan` (TEXT)
- `tanggal_lembur` (DATE)
- `jam_mulai` (TIME)
- `foto_mulai` (VARCHAR - path file)
- `jam_selesai` (TIME, nullable)
- `foto_selesai` (VARCHAR - path file, nullable)
- `status` (ENUM: 'Menunggu', 'Selesai')
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

## File yang Dibuat/Dimodifikasi

### 1. `src/pages/Lembur.tsx`
- Komponen utama halaman lembur
- Logika form, kamera, dan API calls
- State management untuk data lembur

### 2. `src/pages/Lembur.css`
- Styling khusus untuk halaman lembur
- Responsive design
- Animasi dan transisi
- Dark mode support

### 3. `src/App.tsx`
- Menambahkan import dan route untuk halaman lembur
- Route: `/lembur`

### 4. `src/pages/Home.tsx`
- Menambahkan menu lembur di dashboard
- Icon timeOutline untuk menu lembur

## Cara Menggunakan

### Untuk Karyawan:
1. Login ke aplikasi
2. Pilih menu "Lembur" di dashboard
3. Isi form mulai lembur:
   - Masukkan keterangan pekerjaan
   - Set jam mulai (atau biarkan default)
   - Ambil foto mulai
   - Klik "Mulai Lembur"
4. Setelah lembur selesai:
   - Set jam selesai
   - Ambil foto selesai
   - Klik "Selesai Lembur"
5. Lihat riwayat lembur di bagian bawah halaman

### Untuk Admin:
- Data lembur dapat diakses melalui API GET
- Foto tersimpan di folder `../uploads/` di server
- Status lembur dapat dimonitor melalui database

## Validasi & Error Handling

### Client Side:
- Validasi form wajib diisi
- Validasi foto harus diambil
- Toast notification untuk feedback
- Loading state saat submit

### Server Side:
- Validasi API key
- Validasi data lengkap
- Error handling untuk upload foto
- Response JSON yang konsisten

## Keamanan
- API key validation
- CORS headers
- Base64 encoding untuk foto
- Validasi input data

## Responsive Design
- Mobile-first approach
- Optimized untuk smartphone
- Touch-friendly interface
- Adaptive layout untuk berbagai ukuran layar

## Browser Compatibility
- Modern browsers dengan support WebRTC
- Camera API support
- ES6+ features
- Progressive Web App ready
