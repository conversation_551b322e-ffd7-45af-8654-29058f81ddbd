# Integrasi Aplikasi Mobile dengan Sistem Absensi PDAM

## 📱 Overview

Aplikasi mobile Ionic React telah diintegrasikan dengan sistem absensi PDAM yang baru. Integrasi ini mencakup fitur-fitur utama seperti login dengan NIK, absensi dengan foto validasi, dan monitoring real-time.

## 🔄 Perubahan Utama

### 1. **Konfigurasi API Baru**
- **File**: `src/config/api.ts`
- **Fitur**: 
  - Konfigurasi endpoint API PDAM
  - Service classes untuk Authentication, Attendance, dan Schedule
  - Type definitions untuk data models
  - Storage helper untuk manajemen data lokal

### 2. **Halaman Login Baru**
- **File**: `src/pages/Login.tsx` (updated)
- **Perubahan**:
  - Login menggunakan NIK saja (tanpa password)
  - Integrasi dengan API PDAM
  - Validasi NIK real-time
  - UI yang disederhanakan

### 3. **Dashboard Home Baru**
- **File**: `src/pages/HomeNew.tsx`
- **Fitur**:
  - Dashboard real-time dengan jam digital
  - Status absensi hari ini
  - Informasi jadwal kerja
  - Riwayat absensi 7 hari terakhir
  - Quick actions untuk absensi dan riwayat

### 4. **Halaman Absensi dengan Foto**
- **File**: `src/pages/AbsensiNew.tsx`
- **Fitur**:
  - Foto wajib untuk validasi absensi
  - GPS location tracking
  - Informasi jadwal kerja real-time
  - Status absensi hari ini
  - Validasi lokasi dan waktu

## 🛠️ Teknologi yang Digunakan

### Frontend (Mobile App):
- **Ionic React** v8.5.0
- **Capacitor** v7.4.2 untuk native features
- **TypeScript** untuk type safety
- **Capacitor Camera** untuk foto
- **Capacitor Geolocation** untuk GPS

### Backend Integration:
- **REST API** dengan PHP native
- **JSON** untuk data exchange
- **Base64** encoding untuk foto
- **MySQL** database

## 📋 Fitur Utama

### 1. **Authentication**
```typescript
// Login dengan NIK
const loginResponse = await AuthService.login(nik);

// Verifikasi NIK
const verifyResponse = await AuthService.verify(nik);

// Get profile
const profileResponse = await AuthService.getProfile(nik);
```

### 2. **Attendance Management**
```typescript
// Check-in dengan foto
const checkinResponse = await AttendanceService.checkIn({
  nik: user.nik,
  latitude: position.latitude,
  longitude: position.longitude,
  location_id: currentLocation?.id,
  photo: capturedPhoto // base64 encoded
});

// Check-out dengan foto
const checkoutResponse = await AttendanceService.checkOut({
  nik: user.nik,
  latitude: position.latitude,
  longitude: position.longitude,
  photo: capturedPhoto // base64 encoded
});
```

### 3. **Schedule Management**
```typescript
// Get current schedule
const scheduleResponse = await ScheduleService.getCurrent(nik);

// Get monthly schedule
const monthlyResponse = await ScheduleService.getMonthly(nik, year, month);
```

## 🔧 Setup dan Instalasi

### 1. **Install Dependencies**
```bash
cd absensipdam_mobileapp
npm install
```

### 2. **Update API Configuration**
Edit file `src/config/api.ts`:
```typescript
export const API_BASE_URL = 'http://your-server.com/absensipdam/api';
```

### 3. **Build untuk Development**
```bash
# Web development
npm run dev

# Android development
ionic capacitor run android

# iOS development
ionic capacitor run ios
```

### 4. **Build untuk Production**
```bash
# Build web
npm run build

# Build Android
ionic build
ionic capacitor copy android
ionic capacitor run android --prod

# Build iOS
ionic build
ionic capacitor copy ios
ionic capacitor run ios --prod
```

## 📱 Struktur Halaman

### 1. **Login** (`/login`)
- Input NIK karyawan
- Validasi dengan API PDAM
- Auto-redirect ke dashboard

### 2. **Dashboard** (`/home`)
- Informasi karyawan
- Status absensi hari ini
- Jadwal kerja
- Riwayat absensi
- Quick actions

### 3. **Absensi** (`/absensi-new`)
- Foto validasi wajib
- GPS location tracking
- Check-in dan check-out
- Status real-time

### 4. **Riwayat** (`/histori`)
- History absensi
- Filter berdasarkan tanggal
- Detail per hari

## 🔐 Keamanan

### 1. **Authentication**
- NIK-based authentication
- Session management dengan localStorage
- Auto-logout pada session expired

### 2. **Photo Validation**
- Foto wajib untuk setiap absensi
- Base64 encoding untuk transfer
- Validasi ukuran maksimal 5MB

### 3. **Location Validation**
- GPS coordinates required
- Validasi radius lokasi kerja
- Offline location caching

## 📊 Data Models

### Employee
```typescript
interface Employee {
  id: number;
  nik: string;
  name: string;
  department: string;
  position: string;
  email?: string;
  phone?: string;
  is_active: boolean;
}
```

### AttendanceRecord
```typescript
interface AttendanceRecord {
  id: number;
  employee_id: number;
  attendance_date: string;
  check_in_time?: string;
  check_out_time?: string;
  check_in_photo?: string;
  check_out_photo?: string;
  status: 'present' | 'late' | 'absent' | 'half_day' | 'early_leave' | 'overtime';
  notes?: string;
}
```

### WorkSchedule
```typescript
interface WorkSchedule {
  id: number;
  name: string;
  start_time: string;
  end_time: string;
  is_cross_day: boolean;
  early_check_in_limit: number;
  late_check_in_limit: number;
}
```

## 🚀 Deployment

### 1. **Web Deployment**
```bash
npm run build
# Upload dist folder ke web server
```

### 2. **Android APK**
```bash
ionic build
ionic capacitor copy android
cd android
./gradlew assembleRelease
```

### 3. **iOS App Store**
```bash
ionic build
ionic capacitor copy ios
# Open Xcode dan build untuk App Store
```

## 🔍 Testing

### 1. **Unit Testing**
```bash
npm run test
```

### 2. **E2E Testing**
```bash
npm run test.e2e
```

### 3. **Manual Testing**
- Test login dengan NIK valid/invalid
- Test absensi dengan/tanpa foto
- Test GPS location accuracy
- Test offline functionality

## 📞 Support

### API Endpoints:
- **Base URL**: `http://localhost/absensipdam/api`
- **Documentation**: Lihat file `api/README.md`

### Mobile App:
- **Framework**: Ionic React
- **Platform**: Android, iOS, Web
- **Documentation**: File ini dan komentar dalam kode

### Database:
- **Type**: MySQL
- **Schema**: Lihat file `database/absensipdam.sql`

---

**Sistem Absensi PDAM Mobile App v1.0.0**  
Terintegrasi dengan Backend PHP Native dan Database MySQL
