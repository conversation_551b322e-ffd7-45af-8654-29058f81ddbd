# Fitur Rapat - Dokumentasi

## Overview
Fitur rapat telah berhasil diimplementasikan dalam aplikasi absensi PDAM. Fitur ini memungkinkan karyawan untuk melihat daftar rapat dan melakukan absensi menggunakan barcode scanner.

## Fitur yang Diimplementasikan

### 1. Halaman Rapat (`src/pages/Rapat.tsx`)
- **Daftar Rapat**: Menampilkan semua rapat yang tersedia dengan informasi lengkap
- **Status Kehadiran**: Menunjukkan status kehadiran user untuk setiap rapat
- **Scan Barcode**: Tombol untuk melakukan scan barcode absensi
- **Refresh Data**: Pull-to-refresh untuk memperbarui data
- **Indikator Rapat Aktif**: Visual indicator untuk rapat yang sedang berlangsung

### 2. Service API Rapat (`src/utils/rapatService.ts`)
- **RapatService Class**: Service lengkap untuk mengelola API rapat
- **Fetch Data**: Mengambil data rapat dan peserta rapat
- **Update Status**: Mengupdate status kehadiran peserta
- **Barcode Processing**: Memproses scan barcode dan validasi
- **Offline Support**: Menyimpan data ke localStorage untuk akses offline
- **Utility Functions**: Format tanggal, waktu, dan validasi rapat aktif

### 3. Navigasi dan Routing
- **Menu Rapat**: Ditambahkan ke halaman Home dengan ikon people
- **Routing**: Route `/rapat` ditambahkan ke App.tsx
- **Navigation**: Back button dan header yang konsisten

### 4. Styling dan UI/UX (`src/pages/Rapat.css`)
- **Konsisten dengan Tema**: Menggunakan gradient dan styling yang sama dengan Home
- **Responsive Design**: Optimized untuk mobile dan desktop
- **Visual Feedback**: Loading states, toast messages, dan alerts
- **Active Rapat Indicator**: Styling khusus untuk rapat yang sedang berlangsung
- **Badge System**: Status badges untuk kehadiran dan status rapat

## API Endpoints yang Digunakan

### 1. API Rapat
- **URL**: `https://absensiku.trunois.my.id/api/api_rapat.php?api_key=absensiku_api_key_2023`
- **Method**: GET
- **Response**: Daftar semua rapat dengan informasi lengkap termasuk barcode_value

### 2. API Rapat Peserta
- **URL**: `https://absensiku.trunois.my.id/api/api_rapat_peserta.php?api_key=absensiku_api_key_2023`
- **Method**: GET (untuk fetch data), PUT (untuk update status)
- **Response**: Daftar peserta rapat dengan status kehadiran

## Cara Kerja Fitur

### 1. Melihat Daftar Rapat
1. User membuka menu Rapat dari halaman Home
2. Aplikasi mengambil data rapat dari API
3. Aplikasi mengambil data peserta rapat untuk user yang login
4. Menampilkan daftar rapat dengan status kehadiran user

### 2. Scan Barcode Absensi
1. User menekan tombol scan barcode (ikon QR code)
2. Aplikasi meminta permission kamera
3. User mengarahkan kamera ke barcode rapat
4. Aplikasi memproses barcode dan mencari rapat yang sesuai
5. Validasi apakah user terdaftar sebagai peserta
6. Update status kehadiran menjadi "hadir" dengan timestamp
7. Menampilkan feedback sukses/error kepada user

### 3. Validasi dan Error Handling
- **Barcode tidak ditemukan**: Menampilkan pesan error
- **User tidak terdaftar**: Menampilkan pesan warning
- **Sudah absen**: Menampilkan pesan bahwa user sudah melakukan absensi
- **Error koneksi**: Menampilkan pesan error dengan opsi retry

## Dependencies yang Ditambahkan

### 1. Barcode Scanner
```bash
npm install @capacitor-community/barcode-scanner --legacy-peer-deps
```

### 2. Capacitor Configuration
```typescript
// capacitor.config.ts
plugins: {
  BarcodeScanner: {
    androidScanningLibrary: "zxing"
  }
}
```

## File yang Dibuat/Dimodifikasi

### File Baru:
- `src/pages/Rapat.tsx` - Halaman utama rapat
- `src/pages/Rapat.css` - Styling untuk halaman rapat
- `src/utils/rapatService.ts` - Service untuk API rapat

### File yang Dimodifikasi:
- `src/App.tsx` - Menambahkan routing untuk rapat
- `src/pages/Home.tsx` - Menambahkan menu rapat
- `capacitor.config.ts` - Konfigurasi barcode scanner

## Testing dan Deployment

### Development Testing
- Server development berjalan di `http://localhost:5174/`
- Semua komponen telah ditest tanpa error
- UI/UX konsisten dengan aplikasi existing

### Production Deployment
Untuk deployment ke production:
1. Build aplikasi: `npm run build`
2. Sync dengan Capacitor: `npx cap sync`
3. Build untuk Android: `npx cap build android`

## Catatan Penting

### 1. Permissions
- Aplikasi memerlukan permission kamera untuk scan barcode
- Permission akan diminta secara otomatis saat pertama kali scan

### 2. Offline Support
- Data rapat disimpan di localStorage untuk akses offline
- Sync otomatis saat aplikasi online

### 3. Security
- API key sudah dikonfigurasi sesuai dengan sistem existing
- Validasi user dilakukan di sisi server

### 4. Performance
- Lazy loading untuk data rapat
- Optimized rendering untuk daftar rapat
- Efficient state management

## Future Enhancements

### Possible Improvements:
1. **Push Notifications**: Notifikasi untuk rapat yang akan dimulai
2. **QR Code Generation**: Generate QR code untuk rapat baru
3. **Rapat History**: Histori kehadiran rapat user
4. **Export Data**: Export data kehadiran rapat
5. **Real-time Updates**: WebSocket untuk update real-time
6. **Geolocation**: Validasi lokasi saat absensi rapat

## Troubleshooting dan Perbaikan

### Issue yang Diperbaiki:

#### 1. Error "Unexpected end of JSON input"
**Masalah**: API backend mengembalikan response kosong atau format yang tidak sesuai
**Solusi**:
- Menganalisis kode backend API dan menyesuaikan format request
- Backend menggunakan `rapat_id` dan `user_id` untuk update, bukan `id` peserta
- Menggunakan method PUT sesuai dengan backend
- Menambahkan error handling yang lebih robust

#### 2. Format Request yang Salah
**Masalah**: Frontend mengirim `id` peserta, tapi backend mengharapkan `rapat_id` dan `user_id`
**Solusi**:
```typescript
// Sebelum (salah):
const payload = {
  api_key: API_KEY,
  id: pesertaId,
  status: status,
  waktu_hadir: waktu
};

// Sesudah (benar):
const payload = {
  api_key: API_KEY,
  rapat_id: rapatId,
  user_id: userId,
  status: status,
  waktu_hadir: waktu
};
```

#### 3. Method HTTP yang Tidak Sesuai
**Masalah**: Mencoba berbagai method tanpa memahami backend
**Solusi**: Menggunakan PUT method sesuai dengan backend API

### API Backend Structure:
```php
// GET: Ambil semua data peserta rapat
// POST: Tambah peserta baru (cek duplicate)
// PUT: Update status kehadiran (menggunakan rapat_id + user_id)
```

## Support dan Maintenance

Untuk maintenance dan support:
- Monitor API response times
- Update dependencies secara berkala
- Test compatibility dengan versi Android/iOS terbaru
- Backup data rapat secara berkala
- Pastikan format request sesuai dengan backend API
