# 📱 Aplikasi Mobile Absensi PDAM

Aplikasi mobile untuk sistem absensi karyawan PDAM menggunakan Ionic React dengan fitur foto validasi dan GPS tracking.

## 🚀 Quick Start

### 1. **<PERSON><PERSON> dan Install**
```bash
cd absensipdam_mobileapp
npm install
```

### 2. **Konfigurasi API**
Edit file `src/config/api.ts`:
```typescript
export const API_BASE_URL = 'http://localhost/absensipdam/api';
// Ganti dengan URL server Anda
```

### 3. **Jalankan Development**
```bash
# Web browser
npm run dev

# Android (perlu Android Studio)
ionic capacitor run android

# iOS (perlu Xcode - Mac only)
ionic capacitor run ios
```

## 📋 Fitur Utama

### ✅ **Authentication**
- Login menggunakan NIK karyawan
- Validasi real-time dengan database
- Session management otomatis

### 📸 **Absensi dengan Foto**
- **Foto wajib** untuk absen masuk dan pulang
- Validasi identitas karyawan
- Upload foto dengan base64 encoding
- Maksimal ukuran foto: 5MB

### 🗺️ **GPS Location Tracking**
- Validasi lokasi kerja otomatis
- Radius checking untuk area kerja
- Offline location caching

### 📊 **Dashboard Real-time**
- Status absensi hari ini
- Informasi jadwal kerja
- Riwayat absensi 7 hari terakhir
- Jam digital real-time

### 📅 **Manajemen Jadwal**
- Lihat jadwal kerja harian
- Informasi shift dan lokasi
- Support untuk shift lintas hari

## 🛠️ Teknologi

- **Framework**: Ionic React v8.5.0
- **Platform**: Android, iOS, Web
- **Language**: TypeScript
- **Camera**: Capacitor Camera Plugin
- **GPS**: Capacitor Geolocation Plugin
- **Storage**: LocalStorage + Capacitor Preferences

## 📱 Halaman Aplikasi

### 1. **Login** (`/login`)
```
┌─────────────────────────┐
│     🏢 Absensi PDAM     │
│                         │
│  NIK: [____________]    │
│                         │
│     [  MASUK  ]         │
│                         │
│  Sistem menggunakan NIK │
│  sebagai identitas      │
└─────────────────────────┘
```

### 2. **Dashboard** (`/home`)
```
┌─────────────────────────┐
│ 👤 Selamat Datang!      │
│ John Doe - NIK: 123456  │
│ IT Department           │
│                         │
│ ⏰ Status Hari Ini      │
│ [✅ Hadir] 08:00 - --:--│
│                         │
│ 🕐 Jadwal: Shift Pagi   │
│ 08:00 - 17:00           │
│                         │
│ [📷 Absensi] [📊 Riwayat]│
└─────────────────────────┘
```

### 3. **Absensi** (`/absensi-new`)
```
┌─────────────────────────┐
│ 📷 Foto Validasi        │
│                         │
│ [    Foto Preview   ]   │
│                         │
│ [ 📸 Ambil Foto ]       │
│                         │
│ 📍 GPS: ✅ Lokasi Valid │
│                         │
│ [🟢 Absen Masuk]        │
│ [🔴 Absen Pulang]       │
└─────────────────────────┘
```

## 🔧 Setup Development

### Prerequisites
- Node.js 16+ dan npm
- Ionic CLI: `npm install -g @ionic/cli`
- Android Studio (untuk Android)
- Xcode (untuk iOS - Mac only)

### Install Dependencies
```bash
npm install

# Install Capacitor plugins
npm install @capacitor/camera @capacitor/geolocation
```

### Development Commands
```bash
# Web development dengan live reload
npm run dev

# Build untuk production
npm run build

# Sync dengan native platforms
ionic capacitor sync

# Run di Android
ionic capacitor run android

# Run di iOS
ionic capacitor run ios
```

## 📦 Build Production

### Web Build
```bash
npm run build
# Output: dist/ folder
```

### Android APK
```bash
ionic build
ionic capacitor copy android
cd android
./gradlew assembleRelease
# Output: android/app/build/outputs/apk/release/
```

### iOS App
```bash
ionic build
ionic capacitor copy ios
# Buka Xcode dan build manual
```

## 🔐 Konfigurasi Keamanan

### 1. **API Security**
```typescript
// src/config/api.ts
export const API_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
  // Tambahkan API key jika diperlukan
  // 'Authorization': 'Bearer your-api-key'
};
```

### 2. **Photo Security**
- Foto di-encode base64 sebelum upload
- Validasi ukuran maksimal 5MB
- Format yang didukung: JPEG, PNG

### 3. **Location Security**
- GPS coordinates required
- Validasi radius lokasi kerja
- Fallback untuk GPS tidak tersedia

## 🐛 Troubleshooting

### 1. **Login Gagal**
```
❌ NIK tidak valid
✅ Cek koneksi internet
✅ Pastikan NIK terdaftar di database
✅ Cek URL API di config/api.ts
```

### 2. **Foto Tidak Bisa Diambil**
```
❌ Camera permission denied
✅ Berikan permission camera di settings
✅ Restart aplikasi
✅ Cek Capacitor camera plugin
```

### 3. **GPS Tidak Akurat**
```
❌ Location permission denied
✅ Berikan permission location
✅ Aktifkan GPS di device
✅ Coba di area terbuka
```

### 4. **API Connection Error**
```
❌ Network error
✅ Cek koneksi internet
✅ Pastikan server backend running
✅ Cek URL API configuration
✅ Cek CORS settings di backend
```

## 📊 Testing

### Unit Testing
```bash
npm run test
```

### E2E Testing
```bash
npm run test.e2e
```

### Manual Testing Checklist
- [ ] Login dengan NIK valid
- [ ] Login dengan NIK invalid
- [ ] Ambil foto untuk absensi
- [ ] Absen masuk dengan foto
- [ ] Absen pulang dengan foto
- [ ] Lihat riwayat absensi
- [ ] Test offline functionality
- [ ] Test GPS accuracy

## 📞 Support & Contact

### Backend API
- **URL**: `http://localhost/absensipdam/api`
- **Documentation**: `../api/README.md`
- **Test Page**: `http://localhost/absensipdam/test_connection.php`

### Mobile App Issues
- **Framework**: Ionic React
- **Documentation**: https://ionicframework.com/docs/react
- **Capacitor**: https://capacitorjs.com/docs

### Database
- **Type**: MySQL
- **Schema**: `../database/absensipdam.sql`
- **Test**: `http://localhost/absensipdam/test_connection.php`

## 📝 Changelog

### v1.0.0 (Current)
- ✅ Integrasi dengan API PDAM
- ✅ Login dengan NIK
- ✅ Absensi dengan foto validasi
- ✅ GPS location tracking
- ✅ Dashboard real-time
- ✅ Riwayat absensi
- ✅ Support Android & iOS

### Planned Features
- 🔄 Offline mode untuk absensi
- 🔄 Push notifications
- 🔄 Biometric authentication
- 🔄 Schedule swap requests
- 🔄 Leave requests
- 🔄 Overtime tracking

---

**Aplikasi Mobile Absensi PDAM v1.0.0**  
Dikembangkan dengan ❤️ menggunakan Ionic React
