package com.absensi.pdam;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Build;

import java.util.Calendar;

public class AlarmScheduler {

    public static final String ACTION_ABSEN_MASUK = "com.absensi.pdam.ACTION_ABSEN_MASUK";
    public static final String ACTION_ABSEN_PULANG = "com.absensi.pdam.ACTION_ABSEN_PULANG";

    private static final int REQ_MASUK = 1001;
    private static final int REQ_PULANG = 1002;

    public static void scheduleAll(Context context) {
        scheduleMasuk(context);
        schedulePulang(context);
    }

    public static void scheduleMasuk(Context context) {
        long triggerAt = getNextTriggerTime(true);
        scheduleExact(context, ACTION_ABSEN_MASUK, REQ_MASUK, triggerAt);
    }

    public static void schedulePulang(Context context) {
        long triggerAt = getNextTriggerTime(false);
        scheduleExact(context, ACTION_ABSEN_PULANG, REQ_PULANG, triggerAt);
    }

    private static void scheduleExact(Context context, String action, int requestCode, long triggerAtMillis) {
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if (alarmManager == null) return;

        Intent intent = new Intent(context, AlarmReceiver.class);
        intent.setAction(action);

        PendingIntent pi = PendingIntent.getBroadcast(
                context,
                requestCode,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT | (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M ? PendingIntent.FLAG_IMMUTABLE : 0)
        );

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, triggerAtMillis, pi);
        } else {
            alarmManager.setExact(AlarmManager.RTC_WAKEUP, triggerAtMillis, pi);
        }
    }

    // true: masuk, false: pulang
    private static long getNextTriggerTime(boolean isMasuk) {
        Calendar now = Calendar.getInstance();
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);

        int dayOfWeek = now.get(Calendar.DAY_OF_WEEK); // 1=Sunday ... 7=Saturday

        // Target jam/menit default (Senin-Kamis)
        int targetHour = isMasuk ? 7 : 15;
        int targetMinute = isMasuk ? 2 : 30;

        // Jumat (FRIDAY)
        if (dayOfWeek == Calendar.FRIDAY) {
            targetHour = isMasuk ? 6 : 11;
            targetMinute = isMasuk ? 30 : 30;
        }

        // Sabtu/Minggu -> lompat ke Senin berikutnya
        if (dayOfWeek == Calendar.SATURDAY || dayOfWeek == Calendar.SUNDAY) {
            // set ke Senin
            int daysToAdd = ((Calendar.MONDAY + 7) - dayOfWeek) % 7;
            if (daysToAdd == 0) daysToAdd = 1; // just in case
            cal.add(Calendar.DAY_OF_YEAR, daysToAdd);
            cal.set(Calendar.HOUR_OF_DAY, isMasuk ? 7 : 15);
            cal.set(Calendar.MINUTE, isMasuk ? 2 : 30);
            return cal.getTimeInMillis();
        }

        cal.set(Calendar.HOUR_OF_DAY, targetHour);
        cal.set(Calendar.MINUTE, targetMinute);

        // Jika waktu hari ini sudah lewat, geser ke hari berikutnya (dengan aturan khusus Jumat/Senin)
        if (!cal.after(now)) {
            cal.add(Calendar.DAY_OF_YEAR, 1);
            int nextDay = cal.get(Calendar.DAY_OF_WEEK);

            if (nextDay == Calendar.SATURDAY || nextDay == Calendar.SUNDAY) {
                // loncat ke Senin
                int daysToAdd = ((Calendar.MONDAY + 7) - nextDay) % 7;
                if (daysToAdd == 0) daysToAdd = 1;
                cal.add(Calendar.DAY_OF_YEAR, daysToAdd);
                cal.set(Calendar.HOUR_OF_DAY, isMasuk ? 7 : 15);
                cal.set(Calendar.MINUTE, isMasuk ? 2 : 30);
            } else if (nextDay == Calendar.FRIDAY) {
                cal.set(Calendar.HOUR_OF_DAY, isMasuk ? 6 : 11);
                cal.set(Calendar.MINUTE, 30);
            } else {
                cal.set(Calendar.HOUR_OF_DAY, isMasuk ? 7 : 15);
                cal.set(Calendar.MINUTE, isMasuk ? 2 : 30);
            }
        }

        // Jika hari target jatuh pada Jumat, set jam Jumat
        if (cal.get(Calendar.DAY_OF_WEEK) == Calendar.FRIDAY) {
            cal.set(Calendar.HOUR_OF_DAY, isMasuk ? 6 : 11);
            cal.set(Calendar.MINUTE, 30);
        }

        return cal.getTimeInMillis();
    }
}


