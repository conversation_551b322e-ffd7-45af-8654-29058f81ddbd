package com.absensi.pdam;

import android.content.Context;
import android.location.LocationManager;
import android.os.Build;
import android.provider.Settings;
import android.app.AlertDialog;

import com.getcapacitor.BridgeActivity;

public class MainActivity extends BridgeActivity {

    @Override
    public void onResume() {
        super.onResume();

        // Cek Fake GPS
        if (isMockLocationEnabled(this)) {
            // Kirim event ke WebView agar JS bisa melaporkan pelanggaran ke server
            if (getBridge() != null) {
                getBridge().triggerWindowJSEvent("fakeGpsDetected");
            }
            showWarning(
                "Peringatan!",
                "Aplikasi mendeteksi penggunaan Fake GPS.\nAnda telah diblokir dari sistem dan tidak dapat menggunakan aplikasi, Silahkan hubungi admin."
            );
            return;
        }

        // Cek apakah waktu otomatis diaktifkan
        if (!isAutomaticTimeEnabled(this)) {
            // Kirim event ke WebView agar JS bisa melaporkan pelanggaran ke server
            if (getBridge() != null) {
                getBridge().triggerWindowJSEvent("autoTimeDisabled");
            }
            showWarning(
                "Pengaturan Waktu Tidak Otomatis",
                "Silakan aktifkan pengaturan waktu otomatis di perangkat Anda untuk menggunakan aplikasi."
            );
        }

        // Jadwalkan alarm pengingat absen (tidak mengganggu fitur yang sudah ada)
        try {
            AlarmScheduler.scheduleAll(this);
        } catch (Exception ignored) {}
    }

    private boolean isMockLocationEnabled(Context context) {
        boolean isMock = false;

        try {
            // Untuk Android < 6.0
            if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.LOLLIPOP_MR1) {
                isMock = !Settings.Secure.getString(
                        context.getContentResolver(),
                        Settings.Secure.ALLOW_MOCK_LOCATION
                ).equals("0");
            } 
            // Untuk Android >= 6.0
            else {
                LocationManager locationManager = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);
                if (locationManager != null && locationManager.getAllProviders() != null) {
                    for (String provider : locationManager.getAllProviders()) {
                        try {
                            android.location.Location location = locationManager.getLastKnownLocation(provider);
                            if (location != null && location.isFromMockProvider()) {
                                isMock = true;
                                break;
                            }
                        } catch (SecurityException ignored) {}
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return isMock;
    }

    private boolean isAutomaticTimeEnabled(Context context) {
        // Untuk Android < 4.2 (Jelly Bean MR1)
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.JELLY_BEAN_MR1) {
            return Settings.System.getInt(
                    context.getContentResolver(),
                    Settings.System.AUTO_TIME, 0
            ) == 1;
        } else {
            return Settings.Global.getInt(
                    context.getContentResolver(),
                    Settings.Global.AUTO_TIME, 0
            ) == 1;
        }
    }

    private void showWarning(String title, String message) {
        new AlertDialog.Builder(this)
                .setTitle(title)
                .setMessage(message)
                .setCancelable(false)
                .setPositiveButton("Keluar", (dialog, which) -> {
                    finishAffinity(); // Menutup semua activity
                    System.exit(0);   // Keluar aplikasi
                })
                .show();
    }
}
