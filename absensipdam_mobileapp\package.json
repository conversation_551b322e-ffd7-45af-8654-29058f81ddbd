{"name": "absensipdam", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test.e2e": "cypress run", "test.unit": "vitest", "lint": "eslint"}, "dependencies": {"@capacitor-community/barcode-scanner": "^4.0.1", "@capacitor/android": "^7.4.2", "@capacitor/app": "7.0.1", "@capacitor/camera": "^7.0.1", "@capacitor/core": "^7.4.2", "@capacitor/geolocation": "^7.1.4", "@capacitor/haptics": "7.0.1", "@capacitor/keyboard": "7.0.1", "@capacitor/status-bar": "7.0.1", "@ionic/react": "^8.5.0", "@ionic/react-router": "^8.5.0", "@ionic/storage": "^4.0.0", "@react-google-maps/api": "^2.20.7", "@types/leaflet": "^1.9.20", "@types/react-router": "^5.1.20", "@types/react-router-dom": "^5.3.3", "@zxing/library": "^0.21.3", "cordova-sqlite-storage": "^7.0.0", "ionicons": "^7.4.0", "jsqr": "^1.4.0", "leaflet": "^1.9.4", "localforage-cordovasqlitedriver": "^1.8.0", "maplibre-gl": "^5.6.1", "react": "19.0.0", "react-dom": "19.0.0", "react-leaflet": "^5.0.0", "react-map-gl": "^8.0.4", "react-router": "^5.3.4", "react-router-dom": "^5.3.4"}, "devDependencies": {"@capacitor/cli": "^7.4.2", "@testing-library/dom": ">=7.21.4", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.4.3", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@types/uuid": "^10.0.0", "@vitejs/plugin-legacy": "^5.0.0", "@vitejs/plugin-react": "^4.0.1", "cypress": "^13.5.0", "eslint": "^9.20.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "jsdom": "^22.1.0", "react-scripts": "^5.0.1", "terser": "^5.4.0", "typescript": "^5.1.6", "typescript-eslint": "^8.24.0", "vite": "~5.2.0", "vitest": "^0.34.6"}, "overrides": {"rollup": "4.44.0"}, "description": "An Ionic project"}