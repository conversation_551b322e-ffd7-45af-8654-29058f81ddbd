#!/usr/bin/env node

/**
 * Setup Script untuk Aplikasi Mobile Absensi PDAM
 * Script ini membantu setup awal dan konfigurasi aplikasi
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Setup Aplikasi Mobile Absensi PDAM\n');

// Fungsi helper
const log = (message, type = 'info') => {
  const colors = {
    info: '\x1b[36m',    // cyan
    success: '\x1b[32m', // green
    warning: '\x1b[33m', // yellow
    error: '\x1b[31m',   // red
    reset: '\x1b[0m'     // reset
  };
  console.log(`${colors[type]}${message}${colors.reset}`);
};

const checkFile = (filePath) => {
  return fs.existsSync(filePath);
};

const runCommand = (command, description) => {
  try {
    log(`⏳ ${description}...`);
    execSync(command, { stdio: 'inherit' });
    log(`✅ ${description} berhasil`, 'success');
    return true;
  } catch (error) {
    log(`❌ ${description} gagal: ${error.message}`, 'error');
    return false;
  }
};

// 1. Cek prerequisites
log('1️⃣ Mengecek prerequisites...');

try {
  execSync('node --version', { stdio: 'pipe' });
  log('✅ Node.js terinstall', 'success');
} catch {
  log('❌ Node.js tidak ditemukan. Install Node.js terlebih dahulu.', 'error');
  process.exit(1);
}

try {
  execSync('npm --version', { stdio: 'pipe' });
  log('✅ npm terinstall', 'success');
} catch {
  log('❌ npm tidak ditemukan', 'error');
  process.exit(1);
}

// 2. Install dependencies
log('\n2️⃣ Install dependencies...');
if (!runCommand('npm install', 'Install npm packages')) {
  log('❌ Gagal install dependencies', 'error');
  process.exit(1);
}

// 3. Install Ionic CLI jika belum ada
log('\n3️⃣ Mengecek Ionic CLI...');
try {
  execSync('ionic --version', { stdio: 'pipe' });
  log('✅ Ionic CLI sudah terinstall', 'success');
} catch {
  log('⏳ Install Ionic CLI...', 'warning');
  if (!runCommand('npm install -g @ionic/cli', 'Install Ionic CLI')) {
    log('❌ Gagal install Ionic CLI', 'error');
    process.exit(1);
  }
}

// 4. Cek konfigurasi API
log('\n4️⃣ Mengecek konfigurasi API...');
const apiConfigPath = path.join(__dirname, 'src', 'config', 'api.ts');

if (checkFile(apiConfigPath)) {
  log('✅ File konfigurasi API ditemukan', 'success');
  
  // Baca dan cek konfigurasi
  const apiConfig = fs.readFileSync(apiConfigPath, 'utf8');
  if (apiConfig.includes('localhost')) {
    log('⚠️  API masih menggunakan localhost. Pastikan untuk mengubah URL sesuai server Anda.', 'warning');
  }
} else {
  log('❌ File konfigurasi API tidak ditemukan', 'error');
}

// 5. Setup Capacitor
log('\n5️⃣ Setup Capacitor...');
if (!runCommand('ionic capacitor add android', 'Add Android platform')) {
  log('⚠️  Android platform mungkin sudah ada', 'warning');
}

// 6. Sync Capacitor
log('\n6️⃣ Sync Capacitor...');
runCommand('ionic capacitor sync', 'Sync Capacitor');

// 7. Buat file environment untuk testing
log('\n7️⃣ Setup environment...');
const envContent = `// Environment configuration for PDAM App
export const environment = {
  production: false,
  apiUrl: 'http://localhost/absensipdam/api',
  appName: 'Absensi PDAM',
  version: '1.0.0'
};
`;

const envPath = path.join(__dirname, 'src', 'environments');
if (!fs.existsSync(envPath)) {
  fs.mkdirSync(envPath, { recursive: true });
}

fs.writeFileSync(path.join(envPath, 'environment.ts'), envContent);
log('✅ Environment file dibuat', 'success');

// 8. Buat script helper
log('\n8️⃣ Membuat script helper...');
const packageJsonPath = path.join(__dirname, 'package.json');
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

// Tambahkan script baru
packageJson.scripts = {
  ...packageJson.scripts,
  'dev:pdam': 'ionic serve --lab',
  'build:pdam': 'ionic build',
  'android:pdam': 'ionic capacitor run android',
  'ios:pdam': 'ionic capacitor run ios',
  'sync:pdam': 'ionic capacitor sync',
  'test:pdam': 'npm run test && npm run test.e2e'
};

fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
log('✅ Script helper ditambahkan ke package.json', 'success');

// 9. Informasi final
log('\n🎉 Setup selesai!', 'success');
log('\n📋 Langkah selanjutnya:');
log('1. Edit src/config/api.ts untuk mengatur URL API server');
log('2. Pastikan backend PDAM sudah running');
log('3. Jalankan aplikasi dengan: npm run dev:pdam');
log('4. Untuk Android: npm run android:pdam');
log('5. Untuk iOS: npm run ios:pdam');

log('\n🔧 Script yang tersedia:');
log('• npm run dev:pdam      - Development dengan Ionic Lab');
log('• npm run build:pdam    - Build untuk production');
log('• npm run android:pdam  - Run di Android');
log('• npm run ios:pdam      - Run di iOS');
log('• npm run sync:pdam     - Sync Capacitor');
log('• npm run test:pdam     - Run semua tests');

log('\n📚 Dokumentasi:');
log('• README_PDAM.md        - Panduan lengkap');
log('• PDAM_INTEGRATION.md   - Detail integrasi');
log('• src/config/api.ts     - Konfigurasi API');

log('\n🌐 Testing:');
log('• Web: http://localhost:8100');
log('• API: http://localhost/absensipdam/api');
log('• Test: http://localhost/absensipdam/test_connection.php');

log('\n✨ Selamat menggunakan Aplikasi Mobile Absensi PDAM!', 'success');
