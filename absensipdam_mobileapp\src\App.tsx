import React, { useEffect } from 'react';
import { Redirect, Route, useHistory } from 'react-router-dom';
import { IonApp, IonRouterOutlet, setupIonicReact } from '@ionic/react';
import { IonReactRouter } from '@ionic/react-router';
import Home from './pages/Home';
import HomeNew from './pages/HomeNew';
import Login from './pages/Login';
import Absensi from './pages/Absensi';
import AbsensiNew from './pages/AbsensiNew';
import Histori from './pages/Histori';
import IzinDinas from './pages/IzinDinas';
import HistoriIzinDinas from './pages/HistoriIzinDinas';
import Rapat from './pages/Rapat';
import LaporanHarian from './pages/LaporanHarian';
import Profile from './pages/Profile';
import Lembur from './pages/Lembur';
import BarcodeTest from './components/BarcodeTest';
import GantiPassword from './pages/ganti_password';
import ApiTest from './pages/ApiTest';
import { StorageService } from './config/api';

/* Core CSS required for Ionic components to work properly */
import '@ionic/react/css/core.css';

/* Basic CSS for apps built with Ionic */
import '@ionic/react/css/normalize.css';
import '@ionic/react/css/structure.css';
import '@ionic/react/css/typography.css';

/* Optional CSS utils that can be commented out */
import '@ionic/react/css/padding.css';
import '@ionic/react/css/float-elements.css';
import '@ionic/react/css/text-alignment.css';
import '@ionic/react/css/text-transformation.css';
import '@ionic/react/css/flex-utils.css';
import '@ionic/react/css/display.css';

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* import '@ionic/react/css/palettes/dark.always.css'; */
/* import '@ionic/react/css/palettes/dark.class.css'; */
import '@ionic/react/css/palettes/dark.system.css';

/* Theme variables */
import './theme/variables.css';

setupIonicReact();

const PrivateRoute = ({ children, ...rest }: any) => {
  const isLoggedIn = StorageService.isLoggedIn();
  return (
    <Route
      {...rest}
      render={({ location }) =>
        isLoggedIn ? (
          children
        ) : (
          <Redirect to={{ pathname: '/login', state: { from: location } }} />
        )
      }
    />
  );
};

const Logout: React.FC = () => {
  useEffect(() => {
    StorageService.removeUser();
  }, []);
  return <Redirect to="/login" />;
};

const App: React.FC = () => {
  useEffect(() => {
    // Initialize app
    console.log('PDAM Attendance App initialized');
  }, []);

  // Dengarkan event dari native (MainActivity) untuk pelanggaran perangkat
  useEffect(() => {
    const getNormalizedUser = () => {
      try {
        const raw = localStorage.getItem('user');
        const parsed = raw ? JSON.parse(raw) : {};
        return Array.isArray(parsed) ? (parsed[0] || {}) : parsed;
      } catch {
        return {} as any;
      }
    };

    const shouldThrottle = (key: string, thresholdMs: number) => {
      try {
        const last = localStorage.getItem(key);
        if (!last) return false;
        const diff = Date.now() - new Date(last).getTime();
        return diff < thresholdMs;
      } catch {
        return false;
      }
    };

    const markSent = (key: string) => {
      try { localStorage.setItem(key, new Date().toISOString()); } catch {}
    };

    const reportViolation = async (reason: string, typeKey: string) => {
      const throttleKey = `violation_sent_${typeKey}`;
      if (shouldThrottle(throttleKey, 5 * 60 * 1000)) {
        return; // hindari spam dalam 5 menit
      }

      const user: any = getNormalizedUser();
      const deviceId = localStorage.getItem('device_id') || '';
      const payload = {
        api_key: 'absensiku_api_key_2023',
        user_id: user?.id || user?.nik || '',
        nik: user?.nik || '',
        device_id: deviceId,
        alasan: reason,
      };

      try {
        await fetch('https://absensiku.trunois.my.id/api/blokir_device.php?api_key=absensiku_api_key_2023', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload),
        });
        markSent(throttleKey);
      } catch (e) {
        // diamkan saja, akan dicoba lagi ketika event terjadi lagi setelah throttle
      }
    };

    const onFakeGps = (_e: Event) => {
      reportViolation('Penggunaan Fake GPS terdeteksi', 'fake_gps');
    };
    const onAutoTimeOff = (_e: Event) => {
      reportViolation('Pengaturan waktu otomatis dimatikan', 'auto_time_off');
    };

    window.addEventListener('fakeGpsDetected', onFakeGps as EventListener);
    window.addEventListener('autoTimeDisabled', onAutoTimeOff as EventListener);

    return () => {
      window.removeEventListener('fakeGpsDetected', onFakeGps as EventListener);
      window.removeEventListener('autoTimeDisabled', onAutoTimeOff as EventListener);
    };
  }, []);

  return (
  <IonApp>
    <IonReactRouter>
      <IonRouterOutlet>
          <PrivateRoute exact path="/home">
            <Home />
          </PrivateRoute>
          <PrivateRoute exact path="/home-new">
            <HomeNew />
          </PrivateRoute>
          <PrivateRoute exact path="/absensi">
            <Absensi />
          </PrivateRoute>
          <PrivateRoute exact path="/absensi-new">
            <AbsensiNew />
          </PrivateRoute>
          <PrivateRoute exact path="/histori">
            <Histori />
          </PrivateRoute>
          <PrivateRoute exact path="/izin-dinas">
            <IzinDinas />
          </PrivateRoute>
          <PrivateRoute exact path="/histori-izin-dinas">
            <HistoriIzinDinas />
          </PrivateRoute>
          <PrivateRoute exact path="/rapat">
            <Rapat />
          </PrivateRoute>
          <PrivateRoute exact path="/laporan-harian">
            <LaporanHarian />
          </PrivateRoute>
          <PrivateRoute exact path="/profile">
            <Profile />
          </PrivateRoute>
          <PrivateRoute exact path="/lembur">
            <Lembur />
          </PrivateRoute>
          <PrivateRoute exact path="/barcode-test">
            <BarcodeTest />
          </PrivateRoute>
          <PrivateRoute exact path="/ganti-password">
            <GantiPassword />
          </PrivateRoute>
          <PrivateRoute exact path="/api-test">
            <ApiTest />
          </PrivateRoute>
          <Route exact path="/logout" render={() => {
            StorageService.removeUser();
            window.location.replace('/login');
            return null;
          }} />
          <Route exact path="/login" render={() => {
            const isLoggedIn = StorageService.isLoggedIn();
            return isLoggedIn ? <Redirect to="/home" /> : <Login />;
          }} />
        <Route exact path="/">
            <Redirect to="/login" />
        </Route>
      </IonRouterOutlet>
    </IonReactRouter>
  </IonApp>
);
};

export default App;
