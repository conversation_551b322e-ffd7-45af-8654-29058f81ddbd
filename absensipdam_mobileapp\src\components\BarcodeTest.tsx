import React, { useState } from 'react';
import {
  IonButton,
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonAlert,
  IonToast,
  IonText,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle
} from '@ionic/react';
import { BarcodeScanner } from '@capacitor-community/barcode-scanner';

const BarcodeTest: React.FC = () => {
  const [scanning, setScanning] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastColor, setToastColor] = useState<'success' | 'danger' | 'warning'>('success');
  const [scanResult, setScanResult] = useState<string>('');

  const testBarcodeScanner = async () => {
    try {
      setScanning(true);
      console.log('Starting barcode scanner test...');
      
      // Test 1: Check if BarcodeScanner is available
      if (!BarcodeScanner) {
        throw new Error('BarcodeScanner plugin tidak tersedia');
      }
      console.log('✓ BarcodeScanner plugin tersedia');

      // Test 2: Check permission
      console.log('Checking camera permission...');
      const status = await BarcodeScanner.checkPermission({ force: true });
      console.log('Permission status:', status);
      
      if (!status.granted) {
        if (status.denied) {
          setAlertMessage('Permission kamera ditolak. Silakan aktifkan di pengaturan aplikasi.');
        } else {
          setAlertMessage('Permission kamera diperlukan untuk scan barcode');
        }
        setShowAlert(true);
        return;
      }
      console.log('✓ Camera permission granted');

      // Test 3: Prepare scanner
      console.log('Preparing scanner...');
      document.body.classList.add('scanner-active');
      
      // Test 4: Start scanning
      console.log('Starting scan...');
      const result = await BarcodeScanner.startScan();
      console.log('Scan result:', result);
      
      if (result.hasContent) {
        setScanResult(result.content);
        setToastMessage(`Berhasil scan: ${result.content}`);
        setToastColor('success');
        setShowToast(true);
      } else {
        setToastMessage('Scan dibatalkan atau tidak ada hasil');
        setToastColor('warning');
        setShowToast(true);
      }
      
    } catch (error: any) {
      console.error('Error testing barcode scanner:', error);
      setToastMessage(`Error: ${error.message || 'Gagal melakukan scan barcode'}`);
      setToastColor('danger');
      setShowToast(true);
    } finally {
      setScanning(false);
      document.body.classList.remove('scanner-active');
      try {
        await BarcodeScanner.stopScan();
      } catch (e) {
        console.log('Error stopping scan:', e);
      }
    }
  };

  const checkPermissionOnly = async () => {
    try {
      const status = await BarcodeScanner.checkPermission({ force: false });
      setAlertMessage(`Permission status: ${JSON.stringify(status, null, 2)}`);
      setShowAlert(true);
    } catch (error: any) {
      setAlertMessage(`Error checking permission: ${error.message}`);
      setShowAlert(true);
    }
  };

  const requestPermission = async () => {
    try {
      const status = await BarcodeScanner.checkPermission({ force: true });
      setAlertMessage(`Permission request result: ${JSON.stringify(status, null, 2)}`);
      setShowAlert(true);
    } catch (error: any) {
      setAlertMessage(`Error requesting permission: ${error.message}`);
      setShowAlert(true);
    }
  };

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonTitle>Barcode Scanner Test</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent className="ion-padding">
        <IonCard>
          <IonCardHeader>
            <IonCardTitle>Test Barcode Scanner</IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            <IonButton 
              expand="block" 
              onClick={testBarcodeScanner}
              disabled={scanning}
              color="primary"
              style={{ marginBottom: '10px' }}
            >
              {scanning ? 'Scanning...' : 'Test Full Scan'}
            </IonButton>

            <IonButton 
              expand="block" 
              onClick={checkPermissionOnly}
              fill="outline"
              color="secondary"
              style={{ marginBottom: '10px' }}
            >
              Check Permission Only
            </IonButton>

            <IonButton 
              expand="block" 
              onClick={requestPermission}
              fill="outline"
              color="tertiary"
              style={{ marginBottom: '10px' }}
            >
              Request Permission
            </IonButton>

            {scanResult && (
              <div style={{ marginTop: '20px' }}>
                <IonText color="success">
                  <h3>Hasil Scan Terakhir:</h3>
                  <p>{scanResult}</p>
                </IonText>
              </div>
            )}
          </IonCardContent>
        </IonCard>

        <IonAlert
          isOpen={showAlert}
          onDidDismiss={() => setShowAlert(false)}
          header="Test Result"
          message={alertMessage}
          buttons={['OK']}
        />

        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          duration={3000}
          color={toastColor}
        />
      </IonContent>
    </IonPage>
  );
};

export default BarcodeTest;
