import React, { useState, useEffect } from 'react';
import {
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonText,
  IonSpinner,
  IonRefresher,
  IonRefresherContent,
  IonButton,
  IonIcon,
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonButtons,
  IonContent,
  useIonToast,
  IonSearchbar,
  IonSelect,
  IonSelectOption,
  IonItem,
  IonLabel
} from '@ionic/react';
import { closeOutline, eyeOutline, trashOutline, refreshOutline } from 'ionicons/icons';

interface LaporanHarian {
  id: string;
  nama_karyawan: string;
  periode: string;
  tanggal: string;
  keterangan: string;
  foto: string | null;
  created_at: string;
  updated_at: string;
}

const RiwayatLaporan: React.FC = () => {
  const [laporanList, setLaporanList] = useState<LaporanHarian[]>([]);
  const [filteredLaporan, setFilteredLaporan] = useState<LaporanHarian[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [selectedPeriode, setSelectedPeriode] = useState<string>('');
  const [selectedLaporan, setSelectedLaporan] = useState<LaporanHarian | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [present] = useIonToast();
  const user = JSON.parse(localStorage.getItem('user') || '{}');

  // Fetch laporan dari API
  const fetchLaporan = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        `https://absensiku.trunois.my.id/api/laporan_harian.php?api_key=absensiku_api_key_2023&nama_karyawan=${encodeURIComponent(user.nama || '')}`
      );
      const result = await response.json();

      if (result.status === 'success') {
        setLaporanList(result.data);
        setFilteredLaporan(result.data);
      } else {
        present({
          message: 'Gagal memuat data laporan',
          color: 'danger',
          duration: 3000,
          position: 'top'
        });
      }
    } catch (error) {
      console.error('Error fetching laporan:', error);
      present({
        message: 'Terjadi kesalahan saat memuat data',
        color: 'danger',
        duration: 3000,
        position: 'top'
      });
    } finally {
      setLoading(false);
    }
  };

  // Filter laporan berdasarkan search dan periode
  const filterLaporan = () => {
    let filtered = laporanList;

    // Filter berdasarkan search text (keterangan atau bulan)
    if (searchText) {
      filtered = filtered.filter(laporan =>
        laporan.keterangan.toLowerCase().includes(searchText.toLowerCase()) ||
        laporan.periode.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    // Filter berdasarkan periode bulan
    if (selectedPeriode) {
      filtered = filtered.filter(laporan => laporan.periode === selectedPeriode);
    }

    setFilteredLaporan(filtered);
  };

  // Get unique periode bulan untuk filter
  const getUniquePeriode = () => {
    const periodeSet = new Set(laporanList.map(laporan => laporan.periode));
    return Array.from(periodeSet).sort();
  };

  // Format tanggal
  const formatTanggal = (tanggal: string) => {
    const date = new Date(tanggal);
    return date.toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Get foto URL
  const getFotoUrl = (fotoName: string | null) => {
    if (!fotoName) return null;
    return `https://absensiku.trunois.my.id/uploads/${fotoName}`;
  };

  // Handle refresh
  const handleRefresh = async (event: CustomEvent) => {
    await fetchLaporan();
    event.detail.complete();
  };

  // Handle view detail
  const handleViewDetail = (laporan: LaporanHarian) => {
    setSelectedLaporan(laporan);
    setIsModalOpen(true);
  };

  // Handle delete laporan
  const handleDelete = async (id: string) => {
    try {
      const response = await fetch('https://absensiku.trunois.my.id/api/laporan_harian.php', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          api_key: 'absensiku_api_key_2023',
          id: id
        })
      });

      const result = await response.json();
      if (result.status === 'success') {
        present({
          message: 'Laporan berhasil dihapus',
          color: 'success',
          duration: 3000,
          position: 'top'
        });
        fetchLaporan(); // Refresh data
      } else {
        present({
          message: result.message || 'Gagal menghapus laporan',
          color: 'danger',
          duration: 3000,
          position: 'top'
        });
      }
    } catch (error) {
      console.error('Error deleting laporan:', error);
      present({
        message: 'Terjadi kesalahan saat menghapus laporan',
        color: 'danger',
        duration: 3000,
        position: 'top'
      });
    }
  };

  useEffect(() => {
    fetchLaporan();
  }, []);

  useEffect(() => {
    filterLaporan();
    // eslint-disable-next-line
  }, [searchText, selectedPeriode, laporanList]);

  return (
    <>
      <IonRefresher slot="fixed" onIonRefresh={handleRefresh}>
        <IonRefresherContent></IonRefresherContent>
      </IonRefresher>

      {/* Filter Section */}
      <div style={{ marginBottom: '16px' }}>
        <IonSearchbar
          value={searchText}
          onIonInput={e => setSearchText(e.detail.value!)}
          placeholder="Cari laporan berdasarkan keterangan atau bulan..."
          style={{ '--background': '#f8f9fa', '--border-radius': '12px' }}
        />
        
        <IonItem style={{ '--background': '#f8f9fa', '--border-radius': '12px', marginTop: '8px' }}>
          <IonLabel>Filter Bulan:</IonLabel>
          <IonSelect
            value={selectedPeriode}
            onIonChange={e => setSelectedPeriode(e.detail.value)}
            placeholder="Semua Bulan"
          >
            <IonSelectOption value="">Semua Bulan</IonSelectOption>
            {getUniquePeriode().map(periode => (
              <IonSelectOption key={periode} value={periode}>
                {periode}
              </IonSelectOption>
            ))}
          </IonSelect>
        </IonItem>
      </div>

      {/* Loading */}
      {loading && (
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <IonSpinner name="crescent" />
          <p style={{ marginTop: '16px', color: '#666' }}>Memuat laporan...</p>
        </div>
      )}

      {/* Empty State */}
      {!loading && filteredLaporan.length === 0 && (
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <IonText color="medium">
            <h3>📋 Belum Ada Laporan</h3>
            <p>Belum ada laporan harian yang dibuat.</p>
          </IonText>
          <IonButton fill="outline" onClick={fetchLaporan}>
            <IonIcon icon={refreshOutline} slot="start" />
            Refresh
          </IonButton>
        </div>
      )}

      {/* Laporan List */}
      {!loading && filteredLaporan.map((laporan) => (
        <IonCard key={laporan.id} style={{ marginBottom: '16px' }}>
          <IonCardHeader>
            <IonCardTitle style={{ fontSize: '1.1rem', color: '#1880ff' }}>
              {laporan.periode}
            </IonCardTitle>
            <IonText color="medium">
              <small>{formatTanggal(laporan.tanggal)}</small>
            </IonText>
          </IonCardHeader>
          
          <IonCardContent>
            <p style={{ margin: '0 0 12px 0', lineHeight: 1.5 }}>
              {laporan.keterangan.length > 100 
                ? laporan.keterangan.substring(0, 100) + '...' 
                : laporan.keterangan
              }
            </p>
            
            <div style={{ display: 'flex', gap: '8px', justifyContent: 'flex-end' }}>
              <IonButton 
                size="small" 
                fill="outline" 
                color="primary"
                onClick={() => handleViewDetail(laporan)}
              >
                <IonIcon icon={eyeOutline} slot="start" />
                Detail
              </IonButton>
              
              <IonButton 
                size="small" 
                fill="outline" 
                color="danger"
                onClick={() => handleDelete(laporan.id)}
              >
                <IonIcon icon={trashOutline} slot="start" />
                Hapus
              </IonButton>
            </div>
          </IonCardContent>
        </IonCard>
      ))}

      {/* Detail Modal */}
      <IonModal isOpen={isModalOpen} onDidDismiss={() => setIsModalOpen(false)}>
        <IonHeader>
          <IonToolbar>
            <IonTitle>Detail Laporan</IonTitle>
            <IonButtons slot="end">
              <IonButton onClick={() => setIsModalOpen(false)}>
                <IonIcon icon={closeOutline} />
              </IonButton>
            </IonButtons>
          </IonToolbar>
        </IonHeader>
        
        <IonContent className="ion-padding">
          {selectedLaporan && (
            <div>
              <h2 style={{ color: '#1880ff', marginBottom: '8px' }}>
                {selectedLaporan.periode}
              </h2>
              <p style={{ color: '#666', marginBottom: '16px' }}>
                {formatTanggal(selectedLaporan.tanggal)}
              </p>
              
              <div style={{ marginBottom: '20px' }}>
                <h3>Keterangan:</h3>
                <p style={{ lineHeight: 1.6, whiteSpace: 'pre-wrap' }}>
                  {selectedLaporan.keterangan}
                </p>
              </div>
              
              {selectedLaporan.foto && (
                <div style={{ marginBottom: '20px' }}>
                  <h3>Foto:</h3>
                  <img
                    src={getFotoUrl(selectedLaporan.foto)!}
                    alt="Foto Laporan"
                    style={{
                      width: '100%',
                      maxHeight: '300px',
                      objectFit: 'cover',
                      borderRadius: '8px',
                      border: '1px solid #ddd'
                    }}
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                    }}
                  />
                </div>
              )}
              
              <div style={{ fontSize: '0.9rem', color: '#999' }}>
                <p>Dibuat: {new Date(selectedLaporan.created_at).toLocaleString('id-ID')}</p>
                {selectedLaporan.updated_at !== selectedLaporan.created_at && (
                  <p>Diupdate: {new Date(selectedLaporan.updated_at).toLocaleString('id-ID')}</p>
                )}
              </div>
            </div>
          )}
        </IonContent>
      </IonModal>
    </>
  );
};

export default RiwayatLaporan;
