import React, { useState, useRef, useEffect } from 'react';
import {
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonItem,
  IonLabel,
  IonInput,
  IonTextarea,
  IonButton,
  IonIcon,
  IonText,
  IonNote,
  IonLoading,
  useIonToast
} from '@ionic/react';
import { cameraOutline, checkmarkCircleOutline, imagesOutline, folderOpenOutline } from 'ionicons/icons';

interface TambahLaporanProps {
  onLaporanAdded: () => void;
}

const TambahLaporan: React.FC<TambahLaporanProps> = ({ onLaporanAdded }) => {
  // Get current month and year for default periode
  const getCurrentPeriode = () => {
    const now = new Date();
    const months = [
      'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
      'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
    ];
    return `${months[now.getMonth()]} ${now.getFullYear()}`;
  };

  // Fungsi untuk mendapatkan range tanggal yang diizinkan (hanya bulan ini)
  const getCurrentMonthDateRange = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth(); // 0-11

    // Tanggal pertama bulan ini
    const firstDay = new Date(year, month, 1);
    const minDate = firstDay.toISOString().split('T')[0];

    // Tanggal terakhir bulan ini
    const lastDay = new Date(year, month + 1, 0);
    const maxDate = lastDay.toISOString().split('T')[0];

    return { minDate, maxDate };
  };

  const [formData, setFormData] = useState({
    periode: getCurrentPeriode(),
    tanggal: new Date().toISOString().split('T')[0],
    keterangan: ''
  });
  const [foto, setFoto] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [present] = useIonToast();
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [cameraActive, setCameraActive] = useState(false);
  const [cameraError, setCameraError] = useState<string | null>(null);
  
  const user = JSON.parse(localStorage.getItem('user') || '{}');

  // Cleanup camera on component unmount
  useEffect(() => {
    return () => {
      stopCamera();
    };
    // eslint-disable-next-line
  }, []);



  // Get date range untuk bulan ini saja
  const { minDate, maxDate } = getCurrentMonthDateRange();

  // Start camera
  const startCamera = async () => {
    try {
      setCameraError(null);
      console.log('Starting camera...');

      // Check if running in secure context (HTTPS or localhost)
      if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
        throw new Error('Camera requires HTTPS or localhost');
      }

      // Check if mediaDevices is supported
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Camera API not supported on this device/browser');
      }

      // Check permissions first
      try {
        const permissions = await navigator.permissions.query({ name: 'camera' as PermissionName });
        console.log('Camera permission status:', permissions.state);

        if (permissions.state === 'denied') {
          throw new Error('Camera permission denied. Please allow camera access in browser settings.');
        }
      } catch (permError) {
        console.log('Permission check failed:', permError);
        // Continue anyway, getUserMedia will handle permission
      }

      // Show loading state
      setCameraActive(true);

      // Try different camera configurations with progressive fallback
      let stream = null;
      const constraints = [
        // Try back camera with high quality
        {
          video: {
            facingMode: { exact: 'environment' },
            width: { ideal: 1280 },
            height: { ideal: 720 }
          }
        },
        // Try back camera with lower quality
        {
          video: {
            facingMode: 'environment',
            width: { ideal: 640 },
            height: { ideal: 480 }
          }
        },
        // Try front camera
        {
          video: {
            facingMode: 'user',
            width: { ideal: 640 },
            height: { ideal: 480 }
          }
        },
        // Try any camera with basic constraints
        { video: { width: { ideal: 640 }, height: { ideal: 480 } } },
        // Last resort - any camera
        { video: true }
      ];

      for (let i = 0; i < constraints.length; i++) {
        try {
          console.log(`Trying camera constraint ${i + 1}:`, constraints[i]);
          stream = await navigator.mediaDevices.getUserMedia(constraints[i]);
          console.log('Camera stream obtained:', stream);
          break;
        } catch (constraintError) {
          console.log(`Constraint ${i + 1} failed:`, constraintError);
          if (i === constraints.length - 1) {
            throw constraintError;
          }
        }
      }

      if (stream && videoRef.current) {
        console.log('Setting video stream...');
        videoRef.current.srcObject = stream;

        // Handle video events
        videoRef.current.onloadedmetadata = () => {
          console.log('Video metadata loaded');
          if (videoRef.current) {
            videoRef.current.play().then(() => {
              console.log('Video playing successfully');
            }).catch(playError => {
              console.error('Video play error:', playError);
            });
          }
        };

        videoRef.current.oncanplay = () => {
          console.log('Video can play');
        };

        videoRef.current.onerror = (error) => {
          console.error('Video element error:', error);
          setCameraError('Video stream error');
          stopCamera();
        };

        // Set camera active after stream is set
        // setCameraActive(true); // Already set above

      } else {
        throw new Error('Failed to get video stream or video element not available');
      }
    } catch (err: any) {
      console.error('Camera error:', err);
      setCameraActive(false);

      let errorMessage = 'Tidak dapat mengakses kamera.';

      if (err.name === 'NotAllowedError' || err.message.includes('permission')) {
        errorMessage = 'Izin kamera ditolak. Silakan izinkan akses kamera di pengaturan browser.';
      } else if (err.name === 'NotFoundError') {
        errorMessage = 'Kamera tidak ditemukan pada perangkat ini.';
      } else if (err.name === 'NotSupportedError') {
        errorMessage = 'Kamera tidak didukung pada browser ini.';
      } else if (err.message.includes('HTTPS')) {
        errorMessage = 'Kamera memerlukan koneksi HTTPS yang aman.';
      }

      setCameraError(errorMessage);
      present({
        message: `${errorMessage} Silakan gunakan upload file sebagai alternatif.`,
        color: 'warning',
        duration: 5000,
        position: 'top'
      });
    }
  };

  // Stop camera
  const stopCamera = () => {
    console.log('Stopping camera...');
    try {
      if (videoRef.current && videoRef.current.srcObject) {
        const stream = videoRef.current.srcObject as MediaStream;
        const tracks = stream.getTracks();

        tracks.forEach(track => {
          console.log('Stopping track:', track.kind);
          track.stop();
        });

        videoRef.current.srcObject = null;

        // Clear event handlers
        videoRef.current.onloadedmetadata = null;
        videoRef.current.oncanplay = null;
        videoRef.current.onerror = null;
      }

      setCameraActive(false);
      setCameraError(null);
      console.log('Camera stopped successfully');
    } catch (error) {
      console.error('Error stopping camera:', error);
      setCameraActive(false);
    }
  };

  // Take photo
  const takePhoto = () => {
    try {
      if (videoRef.current && canvasRef.current) {
        const video = videoRef.current;
        const canvas = canvasRef.current;

        // Check if video is ready
        if (video.readyState !== video.HAVE_ENOUGH_DATA) {
          present({
            message: 'Kamera belum siap. Tunggu sebentar dan coba lagi.',
            color: 'warning',
            duration: 3000,
            position: 'top'
          });
          return;
        }

        // Set canvas size to video size
        canvas.width = video.videoWidth || 640;
        canvas.height = video.videoHeight || 480;

        const ctx = canvas.getContext('2d');
        if (ctx) {
          // Draw video frame to canvas
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

          // Convert to base64 with quality compression
          const dataURL = canvas.toDataURL('image/jpeg', 0.8);
          setFoto(dataURL);

          present({
            message: 'Foto berhasil diambil!',
            color: 'success',
            duration: 2000,
            position: 'top'
          });
        }
        stopCamera();
      }
    } catch (error) {
      console.error('Take photo error:', error);
      present({
        message: 'Gagal mengambil foto. Coba lagi atau gunakan upload file.',
        color: 'danger',
        duration: 3000,
        position: 'top'
      });
    }
  };

  // Retake photo
  const retakePhoto = () => {
    setFoto(null);
    startCamera();
  };

  // Remove photo
  const removePhoto = () => {
    setFoto(null);
    stopCamera();
  };

  // Handle file upload from storage
  const handleFileUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        present({
          message: 'Hanya file gambar yang diperbolehkan',
          color: 'warning',
          duration: 3000,
          position: 'top'
        });
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        present({
          message: 'Ukuran file maksimal 5MB',
          color: 'warning',
          duration: 3000,
          position: 'top'
        });
        return;
      }

      // Convert to base64
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          setFoto(e.target.result as string);
          stopCamera(); // Stop camera if active
        }
      };
      reader.onerror = () => {
        present({
          message: 'Gagal membaca file',
          color: 'danger',
          duration: 3000,
          position: 'top'
        });
      };
      reader.readAsDataURL(file);
    }

    // Reset input value
    if (event.target) {
      event.target.value = '';
    }
  };

  // Request camera permission explicitly
  const requestCameraPermission = async () => {
    try {
      console.log('Requesting camera permission...');

      // Try to get permission by requesting a stream and immediately stopping it
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });

      // Stop the stream immediately
      stream.getTracks().forEach(track => track.stop());

      present({
        message: 'Izin kamera berhasil diberikan! Silakan coba ambil foto lagi.',
        color: 'success',
        duration: 3000,
        position: 'top'
      });

      // Now try to start the camera properly
      setTimeout(() => {
        startCamera();
      }, 500);

    } catch (error) {
      console.error('Permission request failed:', error);
      present({
        message: 'Gagal mendapatkan izin kamera. Silakan izinkan akses kamera di pengaturan browser.',
        color: 'danger',
        duration: 5000,
        position: 'top'
      });
    }
  };

  // Handle form submit
  const handleSubmit = async () => {
    // Validasi form
    if (!formData.periode) {
      present({
        message: 'Periode bulan wajib dipilih',
        color: 'warning',
        duration: 3000,
        position: 'top'
      });
      return;
    }

    if (!formData.tanggal) {
      present({
        message: 'Tanggal wajib diisi',
        color: 'warning',
        duration: 3000,
        position: 'top'
      });
      return;
    }

    // Validasi tanggal harus dalam bulan ini
    const selectedDate = formData.tanggal;
    if (selectedDate < minDate || selectedDate > maxDate) {
      present({
        message: `Tanggal harus dalam bulan ini (${minDate} s/d ${maxDate})`,
        color: 'warning',
        duration: 4000,
        position: 'top'
      });
      return;
    }

    if (!formData.keterangan.trim()) {
      present({
        message: 'Keterangan wajib diisi',
        color: 'warning',
        duration: 3000,
        position: 'top'
      });
      return;
    }

    // Validasi tambahan
    if (!user.nama) {
      present({
        message: 'Data user tidak valid. Silakan login ulang.',
        color: 'warning',
        duration: 3000,
        position: 'top'
      });
      return;
    }

    if (formData.keterangan.trim().length < 10) {
      present({
        message: 'Keterangan minimal 10 karakter',
        color: 'warning',
        duration: 3000,
        position: 'top'
      });
      return;
    }

    setSubmitting(true);

    try {
      // Prepare payload
      let fotoToSend = '';

      // Validate foto if exists
      if (foto) {
        try {
          // Check if foto is valid base64
          if (foto.startsWith('data:image/')) {
            fotoToSend = foto;
          } else {
            console.warn('Invalid foto format, sending without foto');
            present({
              message: 'Format foto tidak valid, laporan akan dikirim tanpa foto',
              color: 'warning',
              duration: 3000,
              position: 'top'
            });
          }
        } catch (fotoError) {
          console.error('Foto validation error:', fotoError);
          present({
            message: 'Ada masalah dengan foto, laporan akan dikirim tanpa foto',
            color: 'warning',
            duration: 3000,
            position: 'top'
          });
        }
      }

      const payload = {
        api_key: 'absensiku_api_key_2023',
        nama_karyawan: user.nama,
        periode: formData.periode,
        tanggal: formData.tanggal,
        keterangan: formData.keterangan.trim(),
        foto: fotoToSend
      };

      console.log('Sending payload:', {
        ...payload,
        foto: foto ? `[Base64 image ${foto.length} chars]` : 'No photo'
      });

      const response = await fetch('https://absensiku.trunois.my.id/api/laporan_harian.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      // Check if response is ok
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Server error response:', errorText);

        present({
          message: `Server error (${response.status}): ${response.statusText}`,
          color: 'danger',
          duration: 5000,
          position: 'top'
        });
        return;
      }

      // Try to parse JSON
      let result;
      const responseText = await response.text();
      console.log('Raw response:', responseText);

      try {
        result = JSON.parse(responseText);
      } catch (parseError) {
        console.error('JSON parse error:', parseError);
        console.error('Response text:', responseText);

        present({
          message: 'Server mengembalikan response yang tidak valid. Coba lagi nanti.',
          color: 'danger',
          duration: 5000,
          position: 'top'
        });
        return;
      }

      console.log('Parsed result:', result);

      if (result.status === 'success') {
        // Reset form
        setFormData({
          periode: getCurrentPeriode(),
          tanggal: new Date().toISOString().split('T')[0],
          keterangan: ''
        });
        setFoto(null);
        stopCamera();

        // Callback ke parent
        onLaporanAdded();
      } else {
        present({
          message: result.message || 'Gagal menambahkan laporan',
          color: 'danger',
          duration: 3000,
          position: 'top'
        });
      }
    } catch (error) {
      console.error('Error submitting laporan:', error);

      // More specific error messages
      if (error instanceof TypeError && error.message.includes('fetch')) {
        present({
          message: 'Tidak dapat terhubung ke server. Periksa koneksi internet Anda.',
          color: 'danger',
          duration: 5000,
          position: 'top'
        });
      } else {
        present({
          message: 'Terjadi kesalahan saat mengirim laporan. Coba lagi nanti.',
          color: 'danger',
          duration: 3000,
          position: 'top'
        });
      }
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <>
      <IonCard>
        <IonCardHeader>
          <IonCardTitle style={{ color: '#1880ff' }}>
            ➕ Tambah Laporan Harian
          </IonCardTitle>
        </IonCardHeader>
        
        <IonCardContent>
          {/* Periode */}
          <IonItem>
            <IonLabel position="stacked">Periode Bulan *</IonLabel>
            <IonInput
              value={formData.periode}
              readonly={true}
              placeholder="Periode bulan laporan"
              style={{
                '--color': '#666',
                '--placeholder-color': '#999',
                cursor: 'not-allowed'
              }}
            />
            <IonNote slot="helper" color="medium">
              📅 Periode otomatis sesuai bulan saat ini
            </IonNote>
          </IonItem>

          {/* Tanggal */}
          <IonItem>
            <IonLabel position="stacked">Tanggal *</IonLabel>
            <IonInput
              type="date"
              value={formData.tanggal}
              min={minDate}
              max={maxDate}
              onIonInput={e => setFormData({...formData, tanggal: e.detail.value!})}
            />
            <IonNote slot="helper" color="medium">
              📅 Hanya bisa memilih tanggal di bulan ini ({minDate} s/d {maxDate})
            </IonNote>
          </IonItem>

          {/* Keterangan */}
          <IonItem>
            <IonLabel position="stacked">Keterangan Kegiatan *</IonLabel>
            <IonTextarea
              value={formData.keterangan}
              onIonInput={e => setFormData({...formData, keterangan: e.detail.value!})}
              placeholder="Deskripsikan kegiatan yang dilakukan..."
              rows={4}
              maxlength={1000}
            />
          </IonItem>

          {/* Foto Section */}
          <div style={{ margin: '20px 0' }}>
            <IonText>
              <h3 style={{ margin: '0 0 12px 0' }}>📷 Foto Kegiatan (Opsional)</h3>
            </IonText>

            {!foto && !cameraActive && (
              <div>
                <div style={{ display: 'flex', gap: '8px', margin: '12px 0' }}>
                  <IonButton
                    expand="block"
                    fill="outline"
                    onClick={startCamera}
                    style={{ flex: 1 }}
                  >
                    <IonIcon icon={cameraOutline} slot="start" />
                    Ambil Foto
                  </IonButton>

                  <IonButton
                    expand="block"
                    fill="outline"
                    color="secondary"
                    onClick={handleFileUpload}
                    style={{ flex: 1 }}
                  >
                    <IonIcon icon={imagesOutline} slot="start" />
                    Upload File
                  </IonButton>
                </div>

                {/* Debug info */}
                <div style={{
                  fontSize: '0.8rem',
                  color: '#666',
                  textAlign: 'center',
                  margin: '8px 0'
                }}>
                  💡 Tips: Pastikan aplikasi dizinkan akses kamera
                </div>
              </div>
            )}

            {/* Hidden file input */}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileSelect}
              style={{ display: 'none' }}
            />

            {/* Camera Error Message */}
            {cameraError && !cameraActive && (
              <div style={{
                background: '#fff3cd',
                color: '#856404',
                padding: '12px',
                borderRadius: '8px',
                margin: '12px 0',
                fontSize: '0.9rem'
              }}>
                <div style={{ marginBottom: '8px' }}>
                  ⚠️ {cameraError}
                </div>

                <div style={{ display: 'flex', gap: '8px', marginTop: '8px' }}>
                  {cameraError.includes('izin') || cameraError.includes('permission') ? (
                    <IonButton
                      size="small"
                      fill="outline"
                      color="warning"
                      onClick={requestCameraPermission}
                    >
                      🔓 Minta Izin Kamera
                    </IonButton>
                  ) : (
                    <IonButton
                      size="small"
                      fill="outline"
                      color="warning"
                      onClick={startCamera}
                    >
                      🔄 Coba Lagi
                    </IonButton>
                  )}

                  <IonButton
                    size="small"
                    fill="outline"
                    color="secondary"
                    onClick={handleFileUpload}
                  >
                    📁 Upload File
                  </IonButton>
                </div>
              </div>
            )}

            {cameraActive && (
              <div style={{ marginBottom: '16px' }}>
                <div style={{ position: 'relative' }}>
                  <video
                    ref={videoRef}
                    autoPlay
                    playsInline
                    muted
                    style={{
                      width: '100%',
                      height: '250px',
                      borderRadius: '8px',
                      background: '#000',
                      objectFit: 'cover',
                      display: 'block'
                    }}
                    onLoadedMetadata={() => {
                      console.log('Video loaded metadata');
                    }}
                    onCanPlay={() => {
                      console.log('Video can play');
                    }}
                    onError={(e) => {
                      console.error('Video error:', e);
                      setCameraError('Video stream error');
                      stopCamera();
                    }}
                  />

                  {/* Loading overlay for camera */}
                  {cameraActive && !videoRef.current?.srcObject && (
                    <div style={{
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                      color: 'white',
                      background: 'rgba(0,0,0,0.8)',
                      padding: '12px 20px',
                      borderRadius: '8px',
                      fontSize: '0.9rem',
                      textAlign: 'center'
                    }}>

                    </div>
                  )}
                </div>

                <canvas ref={canvasRef} style={{ display: 'none' }} />

                <div style={{ display: 'flex', gap: '8px', marginTop: '12px' }}>
                  <IonButton
                    expand="block"
                    onClick={takePhoto}
                    style={{ flex: 1 }}
                  >
                    <IonIcon icon={cameraOutline} slot="start" />
                    Ambil Foto
                  </IonButton>
                  <IonButton
                    expand="block"
                    fill="outline"
                    color="medium"
                    onClick={stopCamera}
                    style={{ flex: 1 }}
                  >
                    Batal
                  </IonButton>
                  <IonButton
                    expand="block"
                    fill="outline"
                    color="secondary"
                    onClick={handleFileUpload}
                    style={{ flex: 1 }}
                  >
                    <IonIcon icon={folderOpenOutline} slot="start" />
                    File
                  </IonButton>
                </div>
              </div>
            )}

            {foto && (
              <div style={{ marginBottom: '16px' }}>
                <img
                  src={foto}
                  alt="Foto Kegiatan"
                  style={{
                    width: '100%',
                    maxHeight: '250px',
                    borderRadius: '8px',
                    objectFit: 'cover',
                    border: '1px solid #ddd'
                  }}
                  onError={(e) => {
                    console.error('Image load error:', e);
                    present({
                      message: 'Gagal memuat gambar',
                      color: 'danger',
                      duration: 3000,
                      position: 'top'
                    });
                  }}
                />

                <div style={{ display: 'flex', gap: '8px', marginTop: '12px' }}>
                  <IonButton
                    expand="block"
                    fill="outline"
                    onClick={retakePhoto}
                    style={{ flex: 1 }}
                  >
                    <IonIcon icon={cameraOutline} slot="start" />
                    Kamera
                  </IonButton>
                  <IonButton
                    expand="block"
                    fill="outline"
                    color="secondary"
                    onClick={handleFileUpload}
                    style={{ flex: 1 }}
                  >
                    <IonIcon icon={imagesOutline} slot="start" />
                    Ganti File
                  </IonButton>
                  <IonButton
                    expand="block"
                    fill="outline"
                    color="danger"
                    onClick={removePhoto}
                    style={{ flex: 1 }}
                  >
                    Hapus
                  </IonButton>
                </div>
              </div>
            )}
          </div>

          {/* Submit Button */}
          <IonButton
            expand="block"
            onClick={handleSubmit}
            disabled={submitting}
            style={{
              margin: '10px 0 0 0',
              '--background': '#1880ff',
              '--background-activated': '#005be7'
            }}
          >
            <IonIcon icon={checkmarkCircleOutline} slot="start" />
            {submitting ? 'Mengirim...' : 'Simpan Laporan'}
          </IonButton>
        </IonCardContent>
      </IonCard>

      <IonLoading
        isOpen={submitting}
        message="Menyimpan laporan..."
        duration={0}
      />
    </>
  );
};

export default TambahLaporan;
