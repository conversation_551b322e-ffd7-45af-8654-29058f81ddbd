/**
 * API Configuration for PDAM Attendance System
 * Konfigurasi API untuk Sistem Absensi PDAM
 */

// Base URL untuk API - sesuaikan dengan server Anda
export const API_BASE_URL = 'https://absensipdam.trunois.my.id/api';

// API Endpoints
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: `${API_BASE_URL}/auth.php?action=login`,
    VERIFY: `${API_BASE_URL}/auth.php?action=verify`,
    PROFILE: `${API_BASE_URL}/auth.php?action=profile`,
  },
  
  // Attendance
  ATTENDANCE: {
    CHECKIN: `${API_BASE_URL}/attendance.php?action=checkin`,
    CHECKOUT: `${API_BASE_URL}/attendance.php?action=checkout`,
    HISTORY: `${API_BASE_URL}/attendance.php?action=history`,
    TODAY: `${API_BASE_URL}/attendance.php?action=today`,
    LOCATIONS: `${API_BASE_URL}/attendance.php?action=locations`,
  },
  
  // Schedule
  SCHEDULE: {
    CURRENT: `${API_BASE_URL}/schedule.php?action=current`,
    MONTHLY: `${API_BASE_URL}/schedule.php?action=monthly`,
    SWAPS: `${API_BASE_URL}/schedule.php?action=swaps`,
    REQUEST_SWAP: `${API_BASE_URL}/schedule.php?action=request_swap`,
  }
};

// API Headers
export const API_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
};

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error_code?: string;
}

export interface Employee {
  id: number;
  nik: string;
  name: string;
  department: string;
  position: string;
  email?: string;
  phone?: string;
  is_active: boolean;
}

export interface Location {
  id: number;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  radius: number;
  is_active: boolean;
}

export interface WorkSchedule {
  id: number;
  name: string;
  start_time: string;
  end_time: string;
  is_cross_day: boolean;
  early_check_in_limit: number;
  late_check_in_limit: number;
  early_check_out_limit: number;
  late_check_out_limit: number;
}

export interface AttendanceRecord {
  id: number;
  employee_id: number;
  attendance_date: string;
  check_in_time?: string;
  check_out_time?: string;
  check_in_latitude?: number;
  check_in_longitude?: number;
  check_out_latitude?: number;
  check_out_longitude?: number;
  check_in_photo?: string;
  check_out_photo?: string;
  location_id?: number;
  work_schedule_id?: number;
  status: 'present' | 'late' | 'absent' | 'half_day' | 'early_leave' | 'overtime';
  notes?: string;
}

export interface ScheduleSwap {
  id: number;
  requester_id: number;
  target_id: number;
  requester_date: string;
  target_date: string;
  status: 'pending' | 'approved' | 'rejected';
  reason?: string;
  approved_by?: number;
  approved_at?: string;
}

// API Helper Functions
export class ApiService {
  
  /**
   * Make HTTP request with error handling
   */
  static async request<T>(
    url: string, 
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(url, {
        headers: API_HEADERS,
        ...options,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('API Request Error:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Network error',
        error_code: 'NETWORK_ERROR'
      };
    }
  }

  /**
   * GET request
   */
  static async get<T>(url: string): Promise<ApiResponse<T>> {
    return this.request<T>(url, { method: 'GET' });
  }

  /**
   * POST request
   */
  static async post<T>(url: string, data: any): Promise<ApiResponse<T>> {
    return this.request<T>(url, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  /**
   * PUT request
   */
  static async put<T>(url: string, data: any): Promise<ApiResponse<T>> {
    return this.request<T>(url, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  /**
   * DELETE request
   */
  static async delete<T>(url: string): Promise<ApiResponse<T>> {
    return this.request<T>(url, { method: 'DELETE' });
  }
}

// Authentication Service
export class AuthService {
  
  /**
   * Login with NIK
   */
  static async login(nik: string): Promise<ApiResponse<{ employee: Employee }>> {
    return ApiService.post(API_ENDPOINTS.AUTH.LOGIN, { nik });
  }

  /**
   * Verify NIK
   */
  static async verify(nik: string): Promise<ApiResponse<{ valid: boolean }>> {
    return ApiService.post(API_ENDPOINTS.AUTH.VERIFY, { nik });
  }

  /**
   * Get employee profile
   */
  static async getProfile(nik: string): Promise<ApiResponse<Employee>> {
    return ApiService.get(`${API_ENDPOINTS.AUTH.PROFILE}&nik=${nik}`);
  }
}

// Attendance Service
export class AttendanceService {
  
  /**
   * Check in
   */
  static async checkIn(data: {
    nik: string;
    latitude: number;
    longitude: number;
    location_id?: number;
    photo?: string;
  }): Promise<ApiResponse<{
    attendance_id: number;
    check_in_time: string;
    status: string;
    location: Location;
  }>> {
    return ApiService.post(API_ENDPOINTS.ATTENDANCE.CHECKIN, data);
  }

  /**
   * Check out
   */
  static async checkOut(data: {
    nik: string;
    latitude: number;
    longitude: number;
    photo?: string;
  }): Promise<ApiResponse<{
    attendance_id: number;
    check_out_time: string;
  }>> {
    return ApiService.post(API_ENDPOINTS.ATTENDANCE.CHECKOUT, data);
  }

  /**
   * Get attendance history
   */
  static async getHistory(
    nik: string,
    startDate?: string,
    endDate?: string,
    limit?: number
  ): Promise<ApiResponse<AttendanceRecord[]>> {
    let url = `${API_ENDPOINTS.ATTENDANCE.HISTORY}&nik=${nik}`;
    if (startDate) url += `&start_date=${startDate}`;
    if (endDate) url += `&end_date=${endDate}`;
    if (limit) url += `&limit=${limit}`;
    
    return ApiService.get(url);
  }

  /**
   * Get today's attendance
   */
  static async getToday(nik: string): Promise<ApiResponse<AttendanceRecord>> {
    return ApiService.get(`${API_ENDPOINTS.ATTENDANCE.TODAY}&nik=${nik}`);
  }

  /**
   * Get available locations
   */
  static async getLocations(): Promise<ApiResponse<Location[]>> {
    return ApiService.get(API_ENDPOINTS.ATTENDANCE.LOCATIONS);
  }
}

// Schedule Service
export class ScheduleService {
  
  /**
   * Get current schedule
   */
  static async getCurrent(nik: string): Promise<ApiResponse<{
    schedule: WorkSchedule;
    location: Location;
  }>> {
    return ApiService.get(`${API_ENDPOINTS.SCHEDULE.CURRENT}&nik=${nik}`);
  }

  /**
   * Get monthly schedule
   */
  static async getMonthly(
    nik: string,
    year: number,
    month: number
  ): Promise<ApiResponse<any[]>> {
    return ApiService.get(
      `${API_ENDPOINTS.SCHEDULE.MONTHLY}&nik=${nik}&year=${year}&month=${month}`
    );
  }

  /**
   * Get schedule swaps
   */
  static async getSwaps(nik: string): Promise<ApiResponse<ScheduleSwap[]>> {
    return ApiService.get(`${API_ENDPOINTS.SCHEDULE.SWAPS}&nik=${nik}`);
  }

  /**
   * Request schedule swap
   */
  static async requestSwap(data: {
    nik: string;
    target_nik: string;
    requester_date: string;
    target_date: string;
    reason?: string;
  }): Promise<ApiResponse<{ swap_id: number }>> {
    return ApiService.post(API_ENDPOINTS.SCHEDULE.REQUEST_SWAP, data);
  }

  /**
   * Alias for backward compatibility
   */
  static async getCurrentSchedule(nik: string): Promise<ApiResponse<{
    schedule: WorkSchedule;
    location: Location;
  }>> {
    return this.getCurrent(nik);
  }

  /**
   * Alias for backward compatibility
   */
  static async getMonthlySchedule(
    nik: string,
    year: number,
    month: number
  ): Promise<ApiResponse<any[]>> {
    return this.getMonthly(nik, year, month);
  }
}

// Storage Helper
export class StorageService {
  
  /**
   * Save user data
   */
  static saveUser(user: Employee): void {
    localStorage.setItem('user', JSON.stringify(user));
  }

  /**
   * Get user data
   */
  static getUser(): Employee | null {
    try {
      const userData = localStorage.getItem('user');
      return userData ? JSON.parse(userData) : null;
    } catch {
      return null;
    }
  }

  /**
   * Remove user data
   */
  static removeUser(): void {
    localStorage.removeItem('user');
  }

  /**
   * Check if user is logged in
   */
  static isLoggedIn(): boolean {
    return !!this.getUser();
  }

  /**
   * Alias for backward compatibility
   */
  static setUser(user: Employee): void {
    this.saveUser(user);
  }
}
