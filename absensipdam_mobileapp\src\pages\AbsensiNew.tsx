import React, { useState, useEffect } from 'react';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButton,
  IonIcon,
  IonText,
  IonButtons,
  IonBackButton,
  IonToast,
  IonLoading,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonItem,
  IonLabel,
  IonBadge,
  IonGrid,
  IonRow,
  IonCol,
  IonImg,
  IonAlert
} from '@ionic/react';
import {
  cameraOutline,
  checkmarkCircleOutline,
  locationOutline,
  timeOutline,
  personOutline,
  businessOutline,
  logInOutline,
  logOutOutline,
  refreshOutline
} from 'ionicons/icons';
import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import { Geolocation } from '@capacitor/geolocation';
import { AttendanceService, ScheduleService, StorageService, Employee, Location, WorkSchedule, AttendanceRecord } from '../config/api';

const AbsensiNew: React.FC = () => {
  // State management
  const [user, setUser] = useState<Employee | null>(null);
  const [currentSchedule, setCurrentSchedule] = useState<WorkSchedule | null>(null);
  const [currentLocation, setCurrentLocation] = useState<Location | null>(null);
  const [todayAttendance, setTodayAttendance] = useState<AttendanceRecord | null>(null);
  const [userPosition, setUserPosition] = useState<{ latitude: number; longitude: number } | null>(null);
  const [capturedPhoto, setCapturedPhoto] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastColor, setToastColor] = useState<'success' | 'danger' | 'warning'>('success');
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    setLoading(true);
    try {
      // Get user from storage
      const userData = StorageService.getUser();
      if (!userData) {
        showMessage('User tidak ditemukan', 'danger');
        return;
      }
      setUser(userData);

      // Load current schedule
      await loadCurrentSchedule(userData.nik);
      
      // Load today's attendance
      await loadTodayAttendance(userData.nik);
      
      // Get current position
      await getCurrentPosition();
      
    } catch (error) {
      console.error('Error loading initial data:', error);
      showMessage('Gagal memuat data', 'danger');
    } finally {
      setLoading(false);
    }
  };

  const loadCurrentSchedule = async (nik: string) => {
    try {
      const response = await ScheduleService.getCurrent(nik);
      if (response.success && response.data) {
        setCurrentSchedule(response.data.schedule);
        setCurrentLocation(response.data.location);
      }
    } catch (error) {
      console.error('Error loading schedule:', error);
    }
  };

  const loadTodayAttendance = async (nik: string) => {
    try {
      const response = await AttendanceService.getToday(nik);
      if (response.success && response.data) {
        setTodayAttendance(response.data);
      }
    } catch (error) {
      console.error('Error loading today attendance:', error);
    }
  };

  const getCurrentPosition = async () => {
    try {
      const position = await Geolocation.getCurrentPosition({
        enableHighAccuracy: true,
        timeout: 10000
      });
      
      setUserPosition({
        latitude: position.coords.latitude,
        longitude: position.coords.longitude
      });
    } catch (error) {
      console.error('Error getting position:', error);
      showMessage('Gagal mendapatkan lokasi GPS', 'warning');
    }
  };

  const takePhoto = async () => {
    try {
      const image = await Camera.getPhoto({
        quality: 80,
        allowEditing: false,
        resultType: CameraResultType.DataUrl,
        source: CameraSource.Camera,
        width: 800,
        height: 600
      });

      if (image.dataUrl) {
        setCapturedPhoto(image.dataUrl);
        showMessage('Foto berhasil diambil', 'success');
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      showMessage('Gagal mengambil foto', 'danger');
    }
  };

  const handleCheckIn = async () => {
    if (!user || !userPosition) {
      showMessage('Data user atau lokasi tidak tersedia', 'danger');
      return;
    }

    if (!capturedPhoto) {
      setAlertMessage('Foto wajib diambil untuk validasi absensi masuk. Silakan ambil foto terlebih dahulu.');
      setShowAlert(true);
      return;
    }

    setLoading(true);
    try {
      const response = await AttendanceService.checkIn({
        nik: user.nik,
        latitude: userPosition.latitude,
        longitude: userPosition.longitude,
        location_id: currentLocation?.id,
        photo: capturedPhoto
      });

      if (response.success) {
        showMessage('Absen masuk berhasil!', 'success');
        setCapturedPhoto(null);
        await loadTodayAttendance(user.nik);
      } else {
        showMessage(response.message || 'Absen masuk gagal', 'danger');
      }
    } catch (error) {
      console.error('Error check in:', error);
      showMessage('Terjadi kesalahan saat absen masuk', 'danger');
    } finally {
      setLoading(false);
    }
  };

  const handleCheckOut = async () => {
    if (!user || !userPosition) {
      showMessage('Data user atau lokasi tidak tersedia', 'danger');
      return;
    }

    if (!capturedPhoto) {
      setAlertMessage('Foto wajib diambil untuk validasi absensi pulang. Silakan ambil foto terlebih dahulu.');
      setShowAlert(true);
      return;
    }

    setLoading(true);
    try {
      const response = await AttendanceService.checkOut({
        nik: user.nik,
        latitude: userPosition.latitude,
        longitude: userPosition.longitude,
        photo: capturedPhoto
      });

      if (response.success) {
        showMessage('Absen pulang berhasil!', 'success');
        setCapturedPhoto(null);
        await loadTodayAttendance(user.nik);
      } else {
        showMessage(response.message || 'Absen pulang gagal', 'danger');
      }
    } catch (error) {
      console.error('Error check out:', error);
      showMessage('Terjadi kesalahan saat absen pulang', 'danger');
    } finally {
      setLoading(false);
    }
  };

  const showMessage = (message: string, color: 'success' | 'danger' | 'warning') => {
    setToastMessage(message);
    setToastColor(color);
    setShowToast(true);
  };

  const formatTime = (timeString: string) => {
    return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'present': return 'success';
      case 'late': return 'warning';
      case 'absent': return 'danger';
      default: return 'medium';
    }
  };

  const canCheckIn = () => {
    return !todayAttendance?.check_in_time;
  };

  const canCheckOut = () => {
    return todayAttendance?.check_in_time && !todayAttendance?.check_out_time;
  };

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar color="primary">
          <IonButtons slot="start">
            <IonBackButton defaultHref="/home" />
          </IonButtons>
          <IonTitle>Absensi</IonTitle>
          <IonButtons slot="end">
            <IonButton onClick={loadInitialData}>
              <IonIcon icon={refreshOutline} />
            </IonButton>
          </IonButtons>
        </IonToolbar>
      </IonHeader>

      <IonContent className="ion-padding">
        {/* User Info Card */}
        <IonCard>
          <IonCardHeader>
            <IonCardTitle>
              <IonIcon icon={personOutline} style={{ marginRight: '8px' }} />
              Informasi Karyawan
            </IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            {user && (
              <IonGrid>
                <IonRow>
                  <IonCol size="6">
                    <IonText>
                      <p><strong>NIK:</strong> {user.nik}</p>
                      <p><strong>Nama:</strong> {user.name}</p>
                    </IonText>
                  </IonCol>
                  <IonCol size="6">
                    <IonText>
                      <p><strong>Departemen:</strong> {user.department}</p>
                      <p><strong>Posisi:</strong> {user.position}</p>
                    </IonText>
                  </IonCol>
                </IonRow>
              </IonGrid>
            )}
          </IonCardContent>
        </IonCard>

        {/* Schedule Info Card */}
        {currentSchedule && (
          <IonCard>
            <IonCardHeader>
              <IonCardTitle>
                <IonIcon icon={timeOutline} style={{ marginRight: '8px' }} />
                Jadwal Kerja Hari Ini
              </IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <IonGrid>
                <IonRow>
                  <IonCol size="6">
                    <IonText>
                      <p><strong>Shift:</strong> {currentSchedule.name}</p>
                      <p><strong>Jam Masuk:</strong> {formatTime(currentSchedule.start_time)}</p>
                    </IonText>
                  </IonCol>
                  <IonCol size="6">
                    <IonText>
                      <p><strong>Jam Pulang:</strong> {formatTime(currentSchedule.end_time)}</p>
                      <p><strong>Lokasi:</strong> {currentLocation?.name}</p>
                    </IonText>
                  </IonCol>
                </IonRow>
              </IonGrid>
            </IonCardContent>
          </IonCard>
        )}

        {/* Today's Attendance Status */}
        {todayAttendance && (
          <IonCard>
            <IonCardHeader>
              <IonCardTitle>
                <IonIcon icon={checkmarkCircleOutline} style={{ marginRight: '8px' }} />
                Status Absensi Hari Ini
              </IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <IonItem lines="none">
                <IonLabel>
                  <h3>Status: <IonBadge color={getStatusBadgeColor(todayAttendance.status)}>{todayAttendance.status}</IonBadge></h3>
                  {todayAttendance.check_in_time && (
                    <p>Masuk: {new Date(todayAttendance.check_in_time).toLocaleTimeString('id-ID')}</p>
                  )}
                  {todayAttendance.check_out_time && (
                    <p>Pulang: {new Date(todayAttendance.check_out_time).toLocaleTimeString('id-ID')}</p>
                  )}
                </IonLabel>
              </IonItem>
            </IonCardContent>
          </IonCard>
        )}

        {/* Photo Section */}
        <IonCard>
          <IonCardHeader>
            <IonCardTitle>
              <IonIcon icon={cameraOutline} style={{ marginRight: '8px' }} />
              Foto Validasi
            </IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            {capturedPhoto ? (
              <div style={{ textAlign: 'center' }}>
                <IonImg 
                  src={capturedPhoto} 
                  alt="Captured photo"
                  style={{ 
                    maxWidth: '200px', 
                    maxHeight: '200px', 
                    margin: '0 auto',
                    borderRadius: '8px',
                    border: '2px solid var(--ion-color-primary)'
                  }}
                />
                <IonText color="success">
                  <p style={{ marginTop: '10px' }}>✓ Foto siap untuk absensi</p>
                </IonText>
              </div>
            ) : (
              <div style={{ textAlign: 'center' }}>
                <IonText color="medium">
                  <p>Belum ada foto. Ambil foto untuk validasi absensi.</p>
                </IonText>
              </div>
            )}
            
            <IonButton 
              expand="block" 
              fill="outline" 
              onClick={takePhoto}
              style={{ marginTop: '16px' }}
            >
              <IonIcon icon={cameraOutline} slot="start" />
              {capturedPhoto ? 'Ambil Ulang Foto' : 'Ambil Foto'}
            </IonButton>
          </IonCardContent>
        </IonCard>

        {/* Action Buttons */}
        <IonGrid>
          <IonRow>
            <IonCol size="6">
              <IonButton
                expand="block"
                color="success"
                disabled={!canCheckIn() || loading}
                onClick={handleCheckIn}
              >
                <IonIcon icon={logInOutline} slot="start" />
                Absen Masuk
              </IonButton>
            </IonCol>
            <IonCol size="6">
              <IonButton
                expand="block"
                color="danger"
                disabled={!canCheckOut() || loading}
                onClick={handleCheckOut}
              >
                <IonIcon icon={logOutOutline} slot="start" />
                Absen Pulang
              </IonButton>
            </IonCol>
          </IonRow>
        </IonGrid>

        {/* Location Info */}
        {userPosition && (
          <IonCard>
            <IonCardContent>
              <IonItem lines="none">
                <IonIcon icon={locationOutline} slot="start" color="primary" />
                <IonLabel>
                  <h3>Lokasi GPS</h3>
                  <p>Lat: {userPosition.latitude.toFixed(6)}</p>
                  <p>Lng: {userPosition.longitude.toFixed(6)}</p>
                </IonLabel>
              </IonItem>
            </IonCardContent>
          </IonCard>
        )}

        {/* Toast */}
        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          duration={3000}
          color={toastColor}
        />

        {/* Loading */}
        <IonLoading isOpen={loading} message="Memproses..." />

        {/* Alert */}
        <IonAlert
          isOpen={showAlert}
          onDidDismiss={() => setShowAlert(false)}
          header="Foto Diperlukan"
          message={alertMessage}
          buttons={['OK']}
        />
      </IonContent>
    </IonPage>
  );
};

export default AbsensiNew;
