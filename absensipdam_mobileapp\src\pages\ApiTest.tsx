import React, { useState } from 'react';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButton,
  IonIcon,
  IonText,
  IonButtons,
  IonBackButton,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonItem,
  IonLabel,
  IonBadge,
  IonProgressBar,
  IonList,
  IonTextarea,
  IonGrid,
  IonRow,
  IonCol
} from '@ionic/react';
import {
  playOutline,
  checkmarkCircleOutline,
  closeCircleOutline,
  timeOutline,
  documentTextOutline,
  refreshOutline,
  cloudOutline
} from 'ionicons/icons';
import { ApiTester, TestResult } from '../utils/testApi';

const ApiTest: React.FC = () => {
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);
  const [report, setReport] = useState<string>('');
  const [healthStatus, setHealthStatus] = useState<boolean | null>(null);

  const runTests = async () => {
    setTesting(true);
    setResults([]);
    setReport('');
    
    try {
      const testResults = await ApiTester.runAllTests();
      setResults(testResults);
      
      const testReport = ApiTester.generateReport(testResults);
      setReport(testReport);
    } catch (error) {
      console.error('Error running tests:', error);
    } finally {
      setTesting(false);
    }
  };

  const checkHealth = async () => {
    const isHealthy = await ApiTester.quickHealthCheck();
    setHealthStatus(isHealthy);
  };

  const getStatusIcon = (success: boolean) => {
    return success ? checkmarkCircleOutline : closeCircleOutline;
  };

  const getStatusColor = (success: boolean) => {
    return success ? 'success' : 'danger';
  };

  const formatDuration = (duration?: number) => {
    if (!duration) return '';
    return `${duration}ms`;
  };

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar color="primary">
          <IonButtons slot="start">
            <IonBackButton defaultHref="/home" />
          </IonButtons>
          <IonTitle>API Testing</IonTitle>
        </IonToolbar>
      </IonHeader>

      <IonContent className="ion-padding">
        {/* Health Check Card */}
        <IonCard>
          <IonCardHeader>
            <IonCardTitle>
              <IonIcon icon={cloudOutline} style={{ marginRight: '8px' }} />
              Server Health Check
            </IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            <IonGrid>
              <IonRow className="ion-align-items-center">
                <IonCol>
                  <IonText>
                    <p>Status koneksi ke backend server</p>
                  </IonText>
                </IonCol>
                <IonCol size="auto">
                  {healthStatus !== null && (
                    <IonBadge color={healthStatus ? 'success' : 'danger'}>
                      {healthStatus ? 'Online' : 'Offline'}
                    </IonBadge>
                  )}
                </IonCol>
              </IonRow>
              <IonRow>
                <IonCol>
                  <IonButton 
                    expand="block" 
                    fill="outline" 
                    onClick={checkHealth}
                  >
                    <IonIcon icon={refreshOutline} slot="start" />
                    Cek Status
                  </IonButton>
                </IonCol>
              </IonRow>
            </IonGrid>
          </IonCardContent>
        </IonCard>

        {/* Test Controls */}
        <IonCard>
          <IonCardHeader>
            <IonCardTitle>
              <IonIcon icon={playOutline} style={{ marginRight: '8px' }} />
              API Testing
            </IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            <IonText>
              <p>Test semua endpoint API untuk memastikan integrasi berfungsi dengan baik.</p>
            </IonText>
            
            <IonButton
              expand="block"
              onClick={runTests}
              disabled={testing}
              color="primary"
            >
              <IonIcon icon={playOutline} slot="start" />
              {testing ? 'Testing...' : 'Jalankan Test'}
            </IonButton>
            
            {testing && <IonProgressBar type="indeterminate" />}
          </IonCardContent>
        </IonCard>

        {/* Test Results */}
        {results.length > 0 && (
          <IonCard>
            <IonCardHeader>
              <IonCardTitle>
                <IonIcon icon={documentTextOutline} style={{ marginRight: '8px' }} />
                Hasil Test
              </IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <IonList>
                {results.map((result, index) => (
                  <IonItem key={index} lines={index < results.length - 1 ? 'inset' : 'none'}>
                    <IonIcon 
                      icon={getStatusIcon(result.success)} 
                      color={getStatusColor(result.success)}
                      slot="start" 
                    />
                    <IonLabel>
                      <h3>{result.test}</h3>
                      <p>{result.message}</p>
                      {result.duration && (
                        <p>
                          <IonIcon icon={timeOutline} style={{ fontSize: '12px', marginRight: '4px' }} />
                          {formatDuration(result.duration)}
                        </p>
                      )}
                    </IonLabel>
                    <IonBadge 
                      color={getStatusColor(result.success)} 
                      slot="end"
                    >
                      {result.success ? 'PASS' : 'FAIL'}
                    </IonBadge>
                  </IonItem>
                ))}
              </IonList>
            </IonCardContent>
          </IonCard>
        )}

        {/* Test Report */}
        {report && (
          <IonCard>
            <IonCardHeader>
              <IonCardTitle>
                <IonIcon icon={documentTextOutline} style={{ marginRight: '8px' }} />
                Laporan Lengkap
              </IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <IonTextarea
                value={report}
                readonly
                rows={15}
                style={{
                  fontFamily: 'monospace',
                  fontSize: '12px',
                  background: '#f5f5f5',
                  padding: '10px',
                  border: '1px solid #ddd',
                  borderRadius: '4px'
                }}
              />
            </IonCardContent>
          </IonCard>
        )}

        {/* Info Card */}
        <IonCard>
          <IonCardContent>
            <IonText color="medium">
              <h3>ℹ️ Informasi Testing</h3>
              <p>
                <strong>Endpoint yang ditest:</strong>
              </p>
              <ul>
                <li>Connectivity - Koneksi dasar ke server</li>
                <li>Auth Verify - Verifikasi NIK</li>
                <li>Auth Login - Login dengan NIK</li>
                <li>Attendance Locations - Daftar lokasi</li>
                <li>Attendance Today - Absensi hari ini</li>
                <li>Schedule Current - Jadwal saat ini</li>
              </ul>
              
              <p>
                <strong>Troubleshooting:</strong>
              </p>
              <ul>
                <li>Pastikan backend server running</li>
                <li>Cek URL API di config/api.ts</li>
                <li>Pastikan database terkoneksi</li>
                <li>Cek CORS settings</li>
              </ul>
            </IonText>
          </IonCardContent>
        </IonCard>
      </IonContent>
    </IonPage>
  );
};

export default ApiTest;
