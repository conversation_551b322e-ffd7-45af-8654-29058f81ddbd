import React, { useState, useEffect } from 'react';
import {
  IonPage, IonHeader, IonToolbar, IonTitle, IonContent, IonButton, IonIcon, IonText, IonCard, IonCardContent, IonSpinner, IonToast, IonButtons, IonBackButton, IonBadge, IonFab, IonFabButton, IonAlert, IonItem, IonLabel
} from '@ionic/react';
import {
  addOutline, documentTextOutline, calendarOutline, locationOutline, personOutline, checkmarkCircleOutline, warningOutline, informationCircleOutline, timeOutline, closeCircleOutline
} from 'ionicons/icons';
import { useHistory } from 'react-router-dom';
import './HistoriIzinDinas.css';

interface IzinDinasData {
  id: string;
  user_id: string;
  tanggal_mulai: string;
  tanggal_selesai: string;
  tujuan: string;
  keterangan: string;
  foto_surat_tugas: string;
  foto_wajah: string;
  status: string;
  approved_by: string;
  approved_at: string;
  created_at: string;
  updated_at: string;
  is_notified: string;
}

const HistoriIzinDinas: React.FC = () => {
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const history = useHistory();
  
  // States
  const [izinDinasData, setIzinDinasData] = useState<IzinDinasData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastColor, setToastColor] = useState<'success' | 'danger' | 'warning'>('success');
  const [showLimitAlert, setShowLimitAlert] = useState(false);
  const [monthlyCount, setMonthlyCount] = useState(0);
  const [monthlyBreakdown, setMonthlyBreakdown] = useState({ pending: 0, approved: 0, rejected: 0 });

  // Style untuk header yang selaras dengan halaman lain
  const headerStyle = {
    background: 'linear-gradient(135deg, #1a65eb 0%, #1a65eb 100%)',
    minHeight: 80,
    boxShadow: 'none'
  };

  const titleStyle = {
    color: '#fff',
    fontSize: '1.2rem',
    fontWeight: 'bold',
    textAlign: 'center' as const
  };

  // Fungsi untuk mengambil data izin dinas
  const fetchIzinDinasData = async () => {
    if (!user.id && !user.nik) {
      setError('Data user tidak ditemukan');
      setLoading(false);
      return;
    }

    setLoading(true);
    setError('');
    try {
      const userId = user.id || user.nik;
      const response = await fetch(`https://absensiku.trunois.my.id/api/izin_dinas.php?api_key=absensiku_api_key_2023&user_id=${userId}`);
      const result = await response.json();

      if (result.status === 'success') {
        setIzinDinasData(result.data || []);
        calculateMonthlyCount(result.data || []);
      } else {
        setError(result.message || 'Gagal memuat data izin dinas');
      }
    } catch (error) {
      console.error('Error fetching izin dinas data:', error);
      setError('Terjadi kesalahan saat memuat data');
    } finally {
      setLoading(false);
    }
  };

  // Fungsi untuk menghitung jumlah izin dinas bulan ini (hanya pending dan approved)
  const calculateMonthlyCount = (data: IzinDinasData[]) => {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();

    // Filter data bulan ini
    const monthlyData = data.filter(item => {
      const itemDate = new Date(item.created_at);
      return itemDate.getMonth() === currentMonth && itemDate.getFullYear() === currentYear;
    });

    // Hitung breakdown berdasarkan status
    const breakdown = {
      pending: 0,
      approved: 0,
      rejected: 0
    };

    monthlyData.forEach(item => {
      const status = item.status.toLowerCase();
      if (status === 'pending' || status === 'menunggu') {
        breakdown.pending++;
      } else if (status === 'approved' || status === 'disetujui') {
        breakdown.approved++;
      } else if (status === 'rejected' || status === 'ditolak') {
        breakdown.rejected++;
      }
    });

    setMonthlyBreakdown(breakdown);

    // Hanya hitung pending dan approved untuk kuota
    const validCount = breakdown.pending + breakdown.approved;
    setMonthlyCount(validCount);
  };

  // Fungsi untuk mendapatkan status color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
      case 'disetujui':
        return '#4caf50';
      case 'pending':
      case 'menunggu':
        return '#ff9800';
      case 'rejected':
      case 'ditolak':
        return '#f44336';
      default:
        return '#9e9e9e';
    }
  };

  // Fungsi untuk mendapatkan status icon
  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
      case 'disetujui':
        return checkmarkCircleOutline;
      case 'pending':
      case 'menunggu':
        return timeOutline;
      case 'rejected':
      case 'ditolak':
        return closeCircleOutline;
      default:
        return informationCircleOutline;
    }
  };

  // Fungsi untuk format tanggal
  const formatTanggal = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
  };

  // Fungsi untuk menghitung durasi izin
  const calculateDuration = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
    return diffDays;
  };

  // Fungsi untuk handle tambah izin dinas
  const handleTambahIzinDinas = () => {
    if (monthlyCount >= 4) {
      setShowLimitAlert(true);
      return;
    }
    history.push('/izin-dinas');
  };

  // Helper function untuk toast
  const showToastMessage = (message: string, color: 'success' | 'danger' | 'warning') => {
    setToastMessage(message);
    setToastColor(color);
    setShowToast(true);
  };

  useEffect(() => {
    fetchIzinDinasData();
    // eslint-disable-next-line
  }, []);

  return (
    <IonPage>
      <IonHeader style={headerStyle}>
        <IonToolbar color="transparent" style={{ background: 'transparent', minHeight: 80, boxShadow: 'none' }}>
          <IonButtons slot="start">
            <IonBackButton defaultHref="/home" text="" style={{ color: '#fff', fontSize: 28, marginLeft: 4, background: 'rgba(0, 0, 0, 0)', borderRadius: 12, padding: 4 }} />
          </IonButtons>
          <IonTitle style={titleStyle}>Histori Izin Dinas</IonTitle>
        </IonToolbar>
      </IonHeader>

      <IonContent className="ion-padding">
        {/* Monthly Limit Info */}
        <IonCard style={{ marginBottom: '16px', border: monthlyCount >= 4 ? '2px solid #f44336' : '2px solid #4caf50' }}>
          <IonCardContent>
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'space-between',
              padding: '8px 0'
            }}>
              <div>
                <div style={{ 
                  fontSize: '1.1rem', 
                  fontWeight: 'bold', 
                  color: '#333',
                  marginBottom: '4px',
                  display: 'flex',
                  alignItems: 'center'
                }}>
                  <IonIcon icon={calendarOutline} style={{ marginRight: '8px' }} />
                  Kuota Izin Dinas Bulan Ini
                </div>
                <div style={{ fontSize: '0.9rem', color: '#666' }}>
                  Maksimal 4 kali per bulan (pending + approved)
                </div>
              </div>
              <div style={{ 
                fontSize: '1.5rem', 
                fontWeight: 'bold', 
                color: monthlyCount >= 4 ? '#f44336' : '#4caf50',
                textAlign: 'right'
              }}>
                {monthlyCount}/4
              </div>
            </div>
            {/* Breakdown detail */}
            {(monthlyBreakdown.pending > 0 || monthlyBreakdown.approved > 0 || monthlyBreakdown.rejected > 0) && (
              <div style={{
                marginTop: '12px',
                padding: '8px',
                backgroundColor: '#f8f9fa',
                borderRadius: '4px',
                fontSize: '0.8rem',
                color: '#666'
              }}>
                <div style={{ marginBottom: '4px', fontWeight: 'bold' }}>Detail bulan ini:</div>
                <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
                  {monthlyBreakdown.pending > 0 && (
                    <span style={{ color: '#ff9800' }}>
                      Pending: {monthlyBreakdown.pending}
                    </span>
                  )}
                  {monthlyBreakdown.approved > 0 && (
                    <span style={{ color: '#4caf50' }}>
                      Disetujui: {monthlyBreakdown.approved}
                    </span>
                  )}
                  {monthlyBreakdown.rejected > 0 && (
                    <span style={{ color: '#f44336' }}>
                      Ditolak: {monthlyBreakdown.rejected} (tidak dihitung)
                    </span>
                  )}
                </div>
              </div>
            )}

            {monthlyCount >= 4 && (
              <div style={{
                marginTop: '12px',
                padding: '8px',
                backgroundColor: '#ffebee',
                borderRadius: '4px',
                fontSize: '0.85rem',
                color: '#d32f2f'
              }}>
                ⚠️ Kuota izin dinas bulan ini sudah habis. Anda dapat mengajukan izin dinas lagi bulan depan.
              </div>
            )}
          </IonCardContent>
        </IonCard>

        {/* Loading State */}
        {loading && (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <IonSpinner name="crescent" />
            <p>Memuat data histori izin dinas...</p>
          </div>
        )}

        {/* Error State */}
        {error && !loading && (
          <IonCard>
            <IonCardContent>
              <div style={{ textAlign: 'center', padding: '20px', color: '#f44336' }}>
                <IonIcon icon={warningOutline} style={{ fontSize: '3rem', marginBottom: '16px' }} />
                <p>{error}</p>
                <IonButton onClick={fetchIzinDinasData} size="small">
                  Coba Lagi
                </IonButton>
              </div>
            </IonCardContent>
          </IonCard>
        )}

        {/* Empty State */}
        {!loading && !error && izinDinasData.length === 0 && (
          <IonCard>
            <IonCardContent>
              <div style={{ textAlign: 'center', padding: '50px', color: '#666' }}>
                <IonIcon icon={documentTextOutline} style={{ fontSize: '3rem', marginBottom: '16px' }} />
                <p>Belum ada histori izin dinas</p>
                <IonButton 
                  onClick={handleTambahIzinDinas} 
                  size="small"
                  disabled={monthlyCount >= 4}
                >
                  Ajukan Izin Dinas Pertama
                </IonButton>
              </div>
            </IonCardContent>
          </IonCard>
        )}

        {/* Data List */}
        {!loading && !error && izinDinasData.length > 0 && (
          <>
            {izinDinasData.map((item, index) => (
              <IonCard key={item.id} style={{ marginBottom: '16px' }}>
                <IonCardContent>
                  {/* Header dengan status */}
                  <div style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    marginBottom: '12px'
                  }}>
                    <div style={{ 
                      fontSize: '0.9rem', 
                      color: '#666',
                      display: 'flex',
                      alignItems: 'center'
                    }}>
                      <IonIcon icon={calendarOutline} style={{ marginRight: '4px' }} />
                      {formatTanggal(item.created_at)}
                    </div>
                    <IonBadge 
                      color={item.status === 'approved' || item.status === 'disetujui' ? 'success' : 
                             item.status === 'pending' || item.status === 'menunggu' ? 'warning' : 'danger'}
                      style={{ display: 'flex', alignItems: 'center' }}
                    >
                      <IonIcon 
                        icon={getStatusIcon(item.status)} 
                        style={{ marginRight: '4px', fontSize: '0.8rem' }} 
                      />
                      {item.status}
                    </IonBadge>
                  </div>

                  {/* Periode izin */}
                  <div style={{ marginBottom: '8px' }}>
                    <strong>Periode:</strong> {formatTanggal(item.tanggal_mulai)} - {formatTanggal(item.tanggal_selesai)}
                    <span style={{ 
                      marginLeft: '8px', 
                      fontSize: '0.8rem', 
                      color: '#666',
                      backgroundColor: '#f5f5f5',
                      padding: '2px 6px',
                      borderRadius: '4px'
                    }}>
                      {calculateDuration(item.tanggal_mulai, item.tanggal_selesai)} hari
                    </span>
                  </div>

                  {/* Tujuan */}
                  <div style={{ marginBottom: '8px' }}>
                    <strong>Tujuan:</strong> {item.tujuan}
                  </div>

                  {/* Keterangan */}
                  <div style={{ marginBottom: '12px' }}>
                    <strong>Keterangan:</strong> {item.keterangan}
                  </div>

                  {/* Approval info */}
                  {item.approved_by && item.approved_at && (
                    <div style={{ 
                      fontSize: '0.8rem', 
                      color: '#666',
                      borderTop: '1px solid #eee',
                      paddingTop: '8px'
                    }}>
                      Direspon oleh: {item.approved_by} pada {formatTanggal(item.approved_at)}
                    </div>
                  )}
                </IonCardContent>
              </IonCard>
            ))}
          </>
        )}

        {/* Floating Action Button */}
        <IonFab vertical="bottom" horizontal="end" slot="fixed">
          <IonFabButton 
            onClick={handleTambahIzinDinas}
            disabled={monthlyCount >= 4}
            color={monthlyCount >= 4 ? 'medium' : 'primary'}
          >
            <IonIcon icon={addOutline} />
          </IonFabButton>
        </IonFab>

        {/* Toast */}
        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          duration={3000}
          color={toastColor}
        />

        {/* Limit Alert */}
        <IonAlert
          isOpen={showLimitAlert}
          onDidDismiss={() => setShowLimitAlert(false)}
          header="Kuota Habis"
          message="Anda sudah mengajukan 4 izin dinas bulan ini. Kuota maksimal adalah 4 kali per bulan. Silakan coba lagi bulan depan."
          buttons={['OK']}
        />
      </IonContent>
    </IonPage>
  );
};

export default HistoriIzinDinas;
