import React, { useEffect, useState } from 'react';
import {
  IonContent,
  IonPage,
  IonButton,
  IonIcon,
  IonText,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonButtons,
  IonToast,
  IonRefresher,
  IonRefresherContent,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonGrid,
  IonRow,
  IonCol,
  IonItem,
  IonLabel,
  IonBadge,
  IonAvatar,
  IonChip,
  IonProgressBar
} from '@ionic/react';
import {
  cameraOutline,
  calendarOutline,
  logOutOutline,
  personCircleOutline,
  timeOutline,
  locationOutline,
  checkmarkCircleOutline,
  closeCircleOutline,
  statsChartOutline,
  refreshOutline,
  businessOutline,
  bugOutline
} from 'ionicons/icons';
import { useHistory } from 'react-router-dom';
import { AttendanceService, ScheduleService, StorageService, Employee, AttendanceRecord, WorkSchedule, Location } from '../config/api';
import './Home.css';

const HomeNew: React.FC = () => {
  // State management
  const [user, setUser] = useState<Employee | null>(null);
  const [todayAttendance, setTodayAttendance] = useState<AttendanceRecord | null>(null);
  const [currentSchedule, setCurrentSchedule] = useState<WorkSchedule | null>(null);
  const [currentLocation, setCurrentLocation] = useState<Location | null>(null);
  const [recentAttendance, setRecentAttendance] = useState<AttendanceRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastColor, setToastColor] = useState<'success' | 'danger' | 'warning'>('success');
  
  const history = useHistory();

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    setLoading(true);
    try {
      // Get user from storage
      const userData = StorageService.getUser();
      if (!userData) {
        history.push('/login');
        return;
      }
      setUser(userData);

      // Load data in parallel
      await Promise.all([
        loadTodayAttendance(userData.nik),
        loadCurrentSchedule(userData.nik),
        loadRecentAttendance(userData.nik)
      ]);

    } catch (error) {
      console.error('Error loading initial data:', error);
      showMessage('Gagal memuat data', 'danger');
    } finally {
      setLoading(false);
    }
  };

  const loadTodayAttendance = async (nik: string) => {
    try {
      const response = await AttendanceService.getToday(nik);
      if (response.success && response.data) {
        setTodayAttendance(response.data);
      }
    } catch (error) {
      console.error('Error loading today attendance:', error);
    }
  };

  const loadCurrentSchedule = async (nik: string) => {
    try {
      const response = await ScheduleService.getCurrent(nik);
      if (response.success && response.data) {
        setCurrentSchedule(response.data.schedule);
        setCurrentLocation(response.data.location);
      }
    } catch (error) {
      console.error('Error loading schedule:', error);
    }
  };

  const loadRecentAttendance = async (nik: string) => {
    try {
      const endDate = new Date().toISOString().split('T')[0];
      const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      
      const response = await AttendanceService.getHistory(nik, startDate, endDate, 7);
      if (response.success && response.data) {
        setRecentAttendance(response.data);
      }
    } catch (error) {
      console.error('Error loading recent attendance:', error);
    }
  };

  const handleRefresh = async (event: CustomEvent) => {
    await loadInitialData();
    event.detail.complete();
  };

  const handleLogout = () => {
    StorageService.removeUser();
    history.push('/login');
  };

  const showMessage = (message: string, color: 'success' | 'danger' | 'warning') => {
    setToastMessage(message);
    setToastColor(color);
    setShowToast(true);
  };

  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'present': return 'success';
      case 'late': return 'warning';
      case 'absent': return 'danger';
      case 'early_leave': return 'warning';
      case 'overtime': return 'primary';
      default: return 'medium';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'present': return 'Hadir';
      case 'late': return 'Terlambat';
      case 'absent': return 'Tidak Hadir';
      case 'early_leave': return 'Pulang Cepat';
      case 'overtime': return 'Lembur';
      default: return status;
    }
  };

  const getTodayStatus = () => {
    if (!todayAttendance) return 'Belum Absen';
    if (todayAttendance.check_in_time && !todayAttendance.check_out_time) return 'Sedang Bekerja';
    if (todayAttendance.check_in_time && todayAttendance.check_out_time) return 'Selesai';
    return 'Belum Absen';
  };

  const getCurrentTime = () => {
    return new Date().toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const [currentTime, setCurrentTime] = useState(getCurrentTime());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(getCurrentTime());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar color="primary">
          <IonTitle>Dashboard Absensi</IonTitle>
          <IonButtons slot="end">
            <IonButton onClick={handleLogout}>
              <IonIcon icon={logOutOutline} />
            </IonButton>
          </IonButtons>
        </IonToolbar>
      </IonHeader>

      <IonContent>
        <IonRefresher slot="fixed" onIonRefresh={handleRefresh}>
          <IonRefresherContent></IonRefresherContent>
        </IonRefresher>

        <div className="ion-padding">
          {/* Welcome Card */}
          <IonCard>
            <IonCardContent>
              <IonGrid>
                <IonRow className="ion-align-items-center">
                  <IonCol size="auto">
                    <IonAvatar>
                      <IonIcon icon={personCircleOutline} style={{ fontSize: '48px' }} />
                    </IonAvatar>
                  </IonCol>
                  <IonCol>
                    <IonText>
                      <h2>Selamat Datang!</h2>
                      <p><strong>{user?.name}</strong></p>
                      <p>NIK: {user?.nik}</p>
                      <p>{user?.department} - {user?.position}</p>
                    </IonText>
                  </IonCol>
                  <IonCol size="auto">
                    <IonText color="primary">
                      <h3>{currentTime}</h3>
                      <p>{formatDate(new Date().toISOString())}</p>
                    </IonText>
                  </IonCol>
                </IonRow>
              </IonGrid>
            </IonCardContent>
          </IonCard>

          {/* Today Status Card */}
          <IonCard>
            <IonCardHeader>
              <IonCardTitle>
                <IonIcon icon={checkmarkCircleOutline} style={{ marginRight: '8px' }} />
                Status Hari Ini
              </IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <IonGrid>
                <IonRow>
                  <IonCol size="6">
                    <IonText>
                      <h3>
                        <IonBadge color={todayAttendance ? getStatusBadgeColor(todayAttendance.status) : 'medium'}>
                          {todayAttendance ? getStatusText(todayAttendance.status) : 'Belum Absen'}
                        </IonBadge>
                      </h3>
                      <p>Status: {getTodayStatus()}</p>
                    </IonText>
                  </IonCol>
                  <IonCol size="6">
                    <IonText>
                      {todayAttendance?.check_in_time && (
                        <p>Masuk: {formatTime(todayAttendance.check_in_time)}</p>
                      )}
                      {todayAttendance?.check_out_time && (
                        <p>Pulang: {formatTime(todayAttendance.check_out_time)}</p>
                      )}
                    </IonText>
                  </IonCol>
                </IonRow>
              </IonGrid>
            </IonCardContent>
          </IonCard>

          {/* Schedule Card */}
          {currentSchedule && (
            <IonCard>
              <IonCardHeader>
                <IonCardTitle>
                  <IonIcon icon={timeOutline} style={{ marginRight: '8px' }} />
                  Jadwal Kerja
                </IonCardTitle>
              </IonCardHeader>
              <IonCardContent>
                <IonGrid>
                  <IonRow>
                    <IonCol size="6">
                      <IonText>
                        <p><strong>Shift:</strong> {currentSchedule.name}</p>
                        <p><strong>Jam Kerja:</strong></p>
                        <p>{formatTime(`2000-01-01T${currentSchedule.start_time}`)} - {formatTime(`2000-01-01T${currentSchedule.end_time}`)}</p>
                      </IonText>
                    </IonCol>
                    <IonCol size="6">
                      <IonText>
                        <p><strong>Lokasi:</strong></p>
                        <p>{currentLocation?.name}</p>
                        <p><small>{currentLocation?.address}</small></p>
                      </IonText>
                    </IonCol>
                  </IonRow>
                </IonGrid>
              </IonCardContent>
            </IonCard>
          )}

          {/* Quick Actions */}
          <IonCard>
            <IonCardHeader>
              <IonCardTitle>Aksi Cepat</IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <IonGrid>
                <IonRow>
                  <IonCol size="6">
                    <IonButton 
                      expand="block" 
                      color="primary"
                      onClick={() => history.push('/absensi-new')}
                    >
                      <IonIcon icon={cameraOutline} slot="start" />
                      Absensi
                    </IonButton>
                  </IonCol>
                  <IonCol size="6">
                    <IonButton
                      expand="block"
                      fill="outline"
                      onClick={() => history.push('/histori')}
                    >
                      <IonIcon icon={calendarOutline} slot="start" />
                      Riwayat
                    </IonButton>
                  </IonCol>
                </IonRow>
                <IonRow>
                  <IonCol size="6">
                    <IonButton
                      expand="block"
                      fill="clear"
                      color="medium"
                      onClick={() => history.push('/api-test')}
                    >
                      <IonIcon icon={bugOutline} slot="start" />
                      API Test
                    </IonButton>
                  </IonCol>
                  <IonCol size="6">
                    <IonButton
                      expand="block"
                      fill="clear"
                      color="medium"
                      onClick={() => history.push('/profile')}
                    >
                      <IonIcon icon={personCircleOutline} slot="start" />
                      Profile
                    </IonButton>
                  </IonCol>
                </IonRow>
              </IonGrid>
            </IonCardContent>
          </IonCard>

          {/* Recent Attendance */}
          {recentAttendance.length > 0 && (
            <IonCard>
              <IonCardHeader>
                <IonCardTitle>
                  <IonIcon icon={statsChartOutline} style={{ marginRight: '8px' }} />
                  Riwayat 7 Hari Terakhir
                </IonCardTitle>
              </IonCardHeader>
              <IonCardContent>
                {recentAttendance.map((attendance, index) => (
                  <IonItem key={index} lines={index < recentAttendance.length - 1 ? 'inset' : 'none'}>
                    <IonLabel>
                      <h3>{formatDate(attendance.attendance_date)}</h3>
                      <p>
                        {attendance.check_in_time ? `Masuk: ${formatTime(attendance.check_in_time)}` : 'Tidak masuk'}
                        {attendance.check_out_time && ` | Pulang: ${formatTime(attendance.check_out_time)}`}
                      </p>
                    </IonLabel>
                    <IonBadge color={getStatusBadgeColor(attendance.status)} slot="end">
                      {getStatusText(attendance.status)}
                    </IonBadge>
                  </IonItem>
                ))}
              </IonCardContent>
            </IonCard>
          )}

          {loading && <IonProgressBar type="indeterminate" />}
        </div>

        {/* Toast */}
        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          duration={3000}
          color={toastColor}
        />
      </IonContent>
    </IonPage>
  );
};

export default HomeNew;
