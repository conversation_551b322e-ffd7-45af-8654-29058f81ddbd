import React, { useState, useEffect, useRef } from 'react';
import {
  IonPage, IonHeader, IonToolbar, IonTitle, IonContent, IonButton, IonIcon, IonText, IonCard, IonCardContent, IonItem, IonLabel, IonInput, IonTextarea, IonDatetime, IonSpinner, IonToast, IonButtons, IonBackButton, IonAlert, IonBadge, IonModal
} from '@ionic/react';
import {
  cameraOutline, documentTextOutline, calendarOutline, locationOutline, personOutline, checkmarkCircleOutline, warningOutline, informationCircleOutline, refreshOutline, closeOutline
} from 'ionicons/icons';
// import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import { useHistory } from 'react-router-dom';
import './IzinDinas.css';

interface IzinDinasData {
  id: string;
  user_id: string;
  tanggal_mulai: string;
  tanggal_selesai: string;
  tujuan: string;
  keterangan: string;
  foto_surat_tugas: string;
  foto_wajah: string;
  status: string;
  approved_by: string;
  approved_at: string;
  created_at: string;
  updated_at: string;
  is_notified: string;
}

interface AbsensiHariIni {
  jam_masuk: string | null;
  jam_pulang: string | null;
}

const IzinDinas: React.FC = () => {
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const history = useHistory();

  // Camera refs
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Form states
  const [tanggalMulai, setTanggalMulai] = useState('');
  const [tanggalSelesai, setTanggalSelesai] = useState('');
  const [tujuan, setTujuan] = useState('');
  const [keterangan, setKeterangan] = useState('');
  const [fotoSuratTugas, setFotoSuratTugas] = useState<string>('');
  const [fotoWajah, setFotoWajah] = useState<string>('');

  // Camera states
  const [showCameraModal, setShowCameraModal] = useState(false);
  const [currentPhotoType, setCurrentPhotoType] = useState<'surat' | 'wajah' | null>(null);
  const [tempPhoto, setTempPhoto] = useState<string>('');
  const [cameraError, setCameraError] = useState('');

  // UI states
  const [loading, setLoading] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastColor, setToastColor] = useState<'success' | 'danger' | 'warning'>('success');
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');

  // Data states
  const [absensiHariIni, setAbsensiHariIni] = useState<AbsensiHariIni | null>(null);
  const [loadingAbsensi, setLoadingAbsensi] = useState(true);

  // Style untuk header yang selaras dengan halaman lain
  const headerStyle = {
    background: 'linear-gradient(135deg, #1a65eb 0%, #1a65eb 100%)',
    minHeight: 80,
    boxShadow: 'none'
  };

  const titleStyle = {
    color: '#fff',
    fontSize: '1.2rem',
    fontWeight: 'bold',
    textAlign: 'center' as const
  };

  // Fungsi untuk mengambil data absensi hari ini
  const fetchAbsensiHariIni = async () => {
    if (!user.id && !user.nik) {
      setLoadingAbsensi(false);
      return;
    }
    
    setLoadingAbsensi(true);
    try {
      const today = new Date().toISOString().split('T')[0];
      const userId = user.id || user.nik;
      const response = await fetch(`https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023&user_id=${userId}&tanggal=${today}`);
      const result = await response.json();
      
      if (result.status === 'success' && result.data.length > 0) {
        setAbsensiHariIni({
          jam_masuk: result.data[0].jam_masuk,
          jam_pulang: result.data[0].jam_pulang
        });
      } else {
        setAbsensiHariIni(null);
      }
    } catch (error) {
      console.error('Error fetching absensi hari ini:', error);
      setAbsensiHariIni(null);
    } finally {
      setLoadingAbsensi(false);
    }
  };

  // Fungsi untuk membuka kamera popup
  const openCameraModal = (type: 'surat' | 'wajah') => {
    setCurrentPhotoType(type);
    setTempPhoto('');
    setCameraError('');
    setShowCameraModal(true);
    // Start camera akan dipanggil di useEffect saat modal terbuka
  };

  // Fungsi untuk start kamera
  const startCamera = async () => {
    setCameraError('');
    setTempPhoto('');
    try {
      // Tentukan facing mode berdasarkan jenis foto
      const facingMode = currentPhotoType === 'wajah' ? 'user' : 'environment';

      try {
        // Coba dengan kamera yang diminta
        const stream = await navigator.mediaDevices.getUserMedia({
          video: {
            facingMode: { exact: facingMode }, // Paksa menggunakan kamera yang diminta
            width: { ideal: 1280 },
            height: { ideal: 720 }
          }
        });
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
        }
      } catch (exactError) {
        console.warn(`Kamera ${facingMode} tidak tersedia, mencoba fallback...`);

        // Fallback ke kamera yang tersedia
        const stream = await navigator.mediaDevices.getUserMedia({
          video: {
            facingMode: facingMode, // Tanpa exact, akan menggunakan yang tersedia
            width: { ideal: 1280 },
            height: { ideal: 720 }
          }
        });
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
        }

        // Tampilkan peringatan bahwa menggunakan kamera fallback
        const cameraType = facingMode === 'user' ? 'depan' : 'belakang';
        setCameraError(`Kamera ${cameraType} tidak tersedia. Menggunakan kamera yang tersedia.`);
      }
    } catch (err) {
      setCameraError('Tidak dapat mengakses kamera. Pastikan izin kamera sudah diberikan.');
      console.error('Camera error:', err);
    }
  };

  // Fungsi untuk stop kamera
  const stopCamera = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const tracks = (videoRef.current.srcObject as MediaStream).getTracks();
      tracks.forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }
  };

  // Fungsi untuk ambil foto
  const takePhoto = () => {
    if (videoRef.current && canvasRef.current) {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        setTempPhoto(canvas.toDataURL('image/jpeg', 0.85));
      }
      stopCamera();
    }
  };

  // Fungsi untuk ulangi foto
  const retakePhoto = () => {
    setTempPhoto('');
    startCamera();
  };

  // Fungsi untuk konfirmasi foto
  const confirmPhoto = () => {
    if (tempPhoto && currentPhotoType) {
      if (currentPhotoType === 'surat') {
        setFotoSuratTugas(tempPhoto);
      } else {
        setFotoWajah(tempPhoto);
      }

      const timestamp = new Date().toISOString();
      console.log(`Foto ${currentPhotoType} diambil pada: ${timestamp}`);

      showToastMessage(`Foto ${currentPhotoType === 'surat' ? 'surat tugas' : 'wajah'} berhasil diambil`, 'success');
      closeCameraModal();
    }
  };

  // Fungsi untuk tutup modal kamera
  const closeCameraModal = () => {
    stopCamera();
    setShowCameraModal(false);
    setCurrentPhotoType(null);
    setTempPhoto('');
    setCameraError('');
  };

  // Effect untuk start kamera saat modal terbuka
  useEffect(() => {
    if (showCameraModal) {
      startCamera();
    }
    return () => {
      if (showCameraModal) {
        stopCamera();
      }
    };
    // eslint-disable-next-line
  }, [showCameraModal]);

  // Fungsi untuk submit izin dinas
  const submitIzinDinas = async () => {
    // Validasi user
    if (!user.id && !user.nik) {
      showToastMessage('Data user tidak ditemukan. Silakan login ulang.', 'danger');
      return;
    }

    // Validasi form
    if (!tanggalMulai || !tanggalSelesai || !tujuan || !keterangan) {
      showToastMessage('Mohon lengkapi semua field yang wajib diisi', 'danger');
      return;
    }

    if (!fotoSuratTugas || !fotoWajah) {
      showToastMessage('Mohon ambil foto surat tugas dan foto wajah', 'danger');
      return;
    }

    setLoading(true);
    try {
      // Format tanggal ke YYYY-MM-DD
      const formatTanggal = (dateString: string) => {
        const date = new Date(dateString);
        return date.toISOString().split('T')[0];
      };

      const payload = {
        api_key: 'absensiku_api_key_2023',
        user_id: user.id || user.nik,
        tanggal_mulai: formatTanggal(tanggalMulai),
        tanggal_selesai: formatTanggal(tanggalSelesai),
        tujuan: tujuan,
        keterangan: keterangan,
        foto_surat_tugas_base64: fotoSuratTugas,
        foto_wajah_base64: fotoWajah,
        status: 'pending',
        is_notified: '0'
      };

      console.log('Sending payload:', {
        ...payload,
        foto_surat_tugas_base64: payload.foto_surat_tugas_base64.substring(0, 50) + '...',
        foto_wajah_base64: payload.foto_wajah_base64.substring(0, 50) + '...'
      });

      const response = await fetch('https://absensiku.trunois.my.id/api/izin_dinas.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      // Cek apakah response berhasil
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Server error response:', errorText);
        showToastMessage(`Server error (${response.status}): ${errorText.substring(0, 100)}`, 'danger');
        return;
      }

      // Coba parse JSON
      const responseText = await response.text();
      console.log('Raw response:', responseText);

      let result;
      try {
        result = JSON.parse(responseText);
      } catch (parseError) {
        console.error('JSON parse error:', parseError);
        console.error('Response text:', responseText);
        showToastMessage('Server mengembalikan response yang tidak valid', 'danger');
        return;
      }

      if (result.status === 'success') {
        showToastMessage('Izin dinas berhasil diajukan', 'success');
        resetForm();
        // Kembali ke halaman histori izin dinas setelah 2 detik
        setTimeout(() => {
          history.push('/histori-izin-dinas');
        }, 2000);
      } else {
        showToastMessage(result.message || 'Gagal mengajukan izin dinas', 'danger');
      }
    } catch (error) {
      console.error('Error submitting izin dinas:', error);
      showToastMessage('Terjadi kesalahan koneksi', 'danger');
    } finally {
      setLoading(false);
    }
  };

  // Helper functions
  const showToastMessage = (message: string, color: 'success' | 'danger' | 'warning') => {
    setToastMessage(message);
    setToastColor(color);
    setShowToast(true);
  };

  const resetForm = () => {
    setTanggalMulai('');
    setTanggalSelesai('');
    setTujuan('');
    setKeterangan('');
    setFotoSuratTugas('');
    setFotoWajah('');
  };

  const formatJam = (jam: string | null) => {
    if (!jam) return '-';
    return jam.substring(0, 5);
  };

  useEffect(() => {
    fetchAbsensiHariIni();
  }, []);

  return (
    <IonPage>
      <IonHeader style={headerStyle}>
        <IonToolbar color="transparent" style={{ background: 'transparent', minHeight: 80, boxShadow: 'none' }}>
          <IonButtons slot="start">
            <IonBackButton defaultHref="/histori-izin-dinas" text="" style={{ color: '#fff', fontSize: 28, marginLeft: 4, background: 'rgba(0, 0, 0, 0)', borderRadius: 12, padding: 4 }} />
          </IonButtons>
          <IonTitle style={titleStyle}>Izin Dinas</IonTitle>
        </IonToolbar>
      </IonHeader>

      <IonContent className="ion-padding">
        {/* Info Absensi Hari Ini */}
        {loadingAbsensi ? (
          <IonCard>
            <IonCardContent>
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <IonSpinner name="crescent" />
                <p style={{ margin: '10px 0 0 0', color: '#666' }}>Memuat status absensi...</p>
              </div>
            </IonCardContent>
          </IonCard>
        ) : absensiHariIni?.jam_masuk ? (
          <IonCard style={{ marginBottom: '20px', border: absensiHariIni.jam_pulang ? '2px solid #4caf50' : '2px solid #ff9800' }}>
            <IonCardContent>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                <IonIcon 
                  icon={absensiHariIni.jam_pulang ? checkmarkCircleOutline : informationCircleOutline} 
                  style={{ 
                    fontSize: '1.5rem', 
                    color: absensiHariIni.jam_pulang ? '#4caf50' : '#ff9800',
                    marginRight: '12px' 
                  }} 
                />
                <div>
                  <div style={{ fontWeight: 'bold', fontSize: '1.1rem', color: '#333' }}>
                    Status Absensi Hari Ini
                  </div>
                  <div style={{ fontSize: '0.9rem', color: '#666' }}>
                    Masuk: {formatJam(absensiHariIni.jam_masuk)} | Pulang: {formatJam(absensiHariIni.jam_pulang)}
                  </div>
                </div>
              </div>
              
              {!absensiHariIni.jam_pulang && (
                <div style={{ 
                  padding: '12px', 
                  backgroundColor: '#fff3cd', 
                  borderRadius: '8px',
                  border: '1px solid #ffeaa7'
                }}>
                  <div style={{ 
                    fontSize: '0.9rem', 
                    color: '#856404',
                    fontWeight: 'bold',
                    marginBottom: '4px'
                  }}>
                    ℹ️ Informasi Penting
                  </div>
                  <div style={{ fontSize: '0.85rem', color: '#856404' }}>
                    Anda sudah absen masuk hari ini. Izin dinas ini akan digunakan sebagai pengganti absen pulang.
                  </div>
                </div>
              )}
            </IonCardContent>
          </IonCard>
        ) : null}

        {/* Form Izin Dinas */}
        <IonCard>
          <IonCardContent>
            <div style={{ marginBottom: '20px' }}>
              <h2 style={{ margin: '0 0 8px 0', color: '#333' }}>Form Pengajuan Izin Dinas</h2>
              <p style={{ margin: '0', fontSize: '0.9rem', color: '#666' }}>
                Lengkapi form di bawah untuk mengajukan izin dinas
              </p>
            </div>

            {/* Tanggal Mulai */}
            <IonItem>
              <IonLabel position="stacked">
                Tanggal Mulai <span style={{ color: 'red' }}>*</span>
              </IonLabel>
              <IonDatetime
                value={tanggalMulai}
                onIonChange={e => setTanggalMulai(e.detail.value as string)}
                presentation="date"
                locale="id-ID"
                min={new Date().toISOString().split('T')[0]}
              />
            </IonItem>

            {/* Tanggal Selesai */}
            <IonItem>
              <IonLabel position="stacked">
                Tanggal Selesai <span style={{ color: 'red' }}>*</span>
              </IonLabel>
              <IonDatetime
                value={tanggalSelesai}
                onIonChange={e => setTanggalSelesai(e.detail.value as string)}
                presentation="date"
                locale="id-ID"
                min={tanggalMulai || new Date().toISOString().split('T')[0]}
              />
            </IonItem>

            {/* Tujuan */}
            <IonItem>
              <IonLabel position="stacked">
                Tujuan Dinas <span style={{ color: 'red' }}>*</span>
              </IonLabel>
              <IonInput
                value={tujuan}
                onIonInput={e => setTujuan(e.detail.value!)}
                placeholder="Contoh: Rapat koordinasi di kantor pusat"
              />
            </IonItem>

            {/* Keterangan */}
            <IonItem>
              <IonLabel position="stacked">
                Keterangan <span style={{ color: 'red' }}>*</span>
              </IonLabel>
              <IonTextarea
                value={keterangan}
                onIonInput={e => setKeterangan(e.detail.value!)}
                placeholder="Jelaskan detail kegiatan dinas yang akan dilakukan"
                rows={3}
              />
            </IonItem>
          </IonCardContent>
        </IonCard>

        {/* Foto Section */}
        <IonCard>
          <IonCardContent>
            <div style={{ marginBottom: '20px' }}>
              <h3 style={{ margin: '0 0 8px 0', color: '#333' }}>Upload Dokumen</h3>
              <p style={{ margin: '0', fontSize: '0.9rem', color: '#666' }}>
                Ambil foto surat tugas dan foto wajah untuk verifikasi
              </p>
            </div>

            {/* Foto Surat Tugas */}
            <div style={{ marginBottom: '20px' }}>
              <IonLabel>
                <strong>Foto Surat Tugas <span style={{ color: 'red' }}>*</span></strong>
              </IonLabel>
              <div style={{ marginTop: '8px' }}>
                {fotoSuratTugas ? (
                  <div style={{ textAlign: 'center' }}>
                    <img
                      src={fotoSuratTugas}
                      alt="Foto Surat Tugas"
                      style={{
                        width: '200px',
                        height: '150px',
                        objectFit: 'cover',
                        borderRadius: '8px',
                        border: '2px solid #ddd',
                        marginBottom: '12px'
                      }}
                    />
                    <div>
                      <IonButton
                        size="small"
                        fill="outline"
                        onClick={() => openCameraModal('surat')}
                      >
                        <IonIcon icon={cameraOutline} slot="start" />
                        Ambil Ulang
                      </IonButton>
                    </div>
                  </div>
                ) : (
                  <IonButton
                    expand="block"
                    fill="outline"
                    onClick={() => openCameraModal('surat')}
                  >
                    <IonIcon icon={cameraOutline} slot="start" />
                    Ambil Foto Surat Tugas
                  </IonButton>
                )}
              </div>
            </div>

            {/* Foto Wajah */}
            <div style={{ marginBottom: '20px' }}>
              <IonLabel>
                <strong>Foto Wajah <span style={{ color: 'red' }}>*</span></strong>
              </IonLabel>
              <div style={{ marginTop: '8px' }}>
                {fotoWajah ? (
                  <div style={{ textAlign: 'center' }}>
                    <img
                      src={fotoWajah}
                      alt="Foto Wajah"
                      style={{
                        width: '150px',
                        height: '150px',
                        objectFit: 'cover',
                        borderRadius: '50%',
                        border: '2px solid #ddd',
                        marginBottom: '12px'
                      }}
                    />
                    <div>
                      <IonButton
                        size="small"
                        fill="outline"
                        onClick={() => openCameraModal('wajah')}
                      >
                        <IonIcon icon={cameraOutline} slot="start" />
                        Ambil Ulang
                      </IonButton>
                    </div>
                  </div>
                ) : (
                  <IonButton
                    expand="block"
                    fill="outline"
                    onClick={() => openCameraModal('wajah')}
                  >
                    <IonIcon icon={cameraOutline} slot="start" />
                    Ambil Foto Wajah
                  </IonButton>
                )}
              </div>
            </div>
          </IonCardContent>
        </IonCard>

        {/* Submit Button */}
        <IonCard>
          <IonCardContent>
            <IonButton
              expand="block"
              onClick={submitIzinDinas}
              disabled={loading}
              style={{
                height: '50px',
                fontSize: '1.1rem',
                fontWeight: 'bold'
              }}
            >
              {loading ? (
                <>
                  <IonSpinner name="crescent" style={{ marginRight: '8px' }} />
                  Mengirim...
                </>
              ) : (
                <>
                  <IonIcon icon={documentTextOutline} slot="start" />
                  Ajukan Izin Dinas
                </>
              )}
            </IonButton>

            <div style={{
              marginTop: '12px',
              textAlign: 'center',
              fontSize: '0.8rem',
              color: '#666'
            }}>
              Pastikan semua data sudah benar sebelum mengirim
            </div>
          </IonCardContent>
        </IonCard>

        {/* Toast */}
        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          duration={3000}
          color={toastColor}
        />

        {/* Alert */}
        <IonAlert
          isOpen={showAlert}
          onDidDismiss={() => setShowAlert(false)}
          header="Informasi"
          message={alertMessage}
          buttons={['OK']}
        />

        {/* Camera Modal */}
        <IonModal isOpen={showCameraModal} onDidDismiss={closeCameraModal}>
          <IonHeader>
            <IonToolbar>
              <IonTitle>
                Ambil Foto {currentPhotoType === 'surat' ? 'Surat Tugas' : 'Wajah'}
              </IonTitle>
              <IonButtons slot="end">
                <IonButton onClick={closeCameraModal}>
                  <IonIcon icon={closeOutline} />
                </IonButton>
              </IonButtons>
            </IonToolbar>
            {/* Info kamera yang digunakan */}
            <div style={{
              padding: '8px 16px',
              backgroundColor: 'rgba(26, 101, 235, 0.1)',
              fontSize: '0.85rem',
              color: '#1a65eb',
              textAlign: 'center'
            }}>
              📷 Menggunakan kamera {currentPhotoType === 'surat' ? 'belakang' : 'depan'}
              {currentPhotoType === 'surat' ? ' untuk foto dokumen' : ' untuk foto wajah'}
            </div>
          </IonHeader>
          <IonContent>
            <div style={{
              padding: '16px',
              display: 'flex',
              flexDirection: 'column',
              height: '100%'
            }}>
              {/* Camera Preview */}
              <div style={{
                flex: 1,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                marginBottom: '16px'
              }}>
                {!tempPhoto ? (
                  <video
                    ref={videoRef}
                    autoPlay
                    playsInline
                    style={{
                      width: '100%',
                      maxHeight: '70vh',
                      borderRadius: '12px',
                      background: '#000',
                      objectFit: 'cover'
                    }}
                  />
                ) : (
                  <img
                    src={tempPhoto}
                    alt="Preview Foto"
                    style={{
                      width: '100%',
                      maxHeight: '70vh',
                      borderRadius: '12px',
                      objectFit: 'cover'
                    }}
                  />
                )}
                <canvas ref={canvasRef} style={{ display: 'none' }} />
              </div>

              {/* Error Message */}
              {cameraError && (
                <IonText color="danger">
                  <p style={{ textAlign: 'center', margin: '8px 0' }}>{cameraError}</p>
                </IonText>
              )}

              {/* Camera Controls */}
              <div style={{
                display: 'flex',
                justifyContent: 'center',
                gap: '12px',
                marginTop: 'auto',
                paddingBottom: '16px'
              }}>
                {!tempPhoto ? (
                  <IonButton
                    color="primary"
                    onClick={takePhoto}
                    size="large"
                    shape="round"
                    disabled={!!cameraError}
                  >
                    <IonIcon icon={cameraOutline} slot="start" />
                    Ambil Foto
                  </IonButton>
                ) : (
                  <>
                    <IonButton
                      color="medium"
                      onClick={retakePhoto}
                      size="large"
                      shape="round"
                    >
                      <IonIcon icon={refreshOutline} slot="start" />
                      Ulangi
                    </IonButton>
                    <IonButton
                      color="success"
                      onClick={confirmPhoto}
                      size="large"
                      shape="round"
                    >
                      <IonIcon icon={checkmarkCircleOutline} slot="start" />
                      Gunakan
                    </IonButton>
                  </>
                )}
              </div>
            </div>
          </IonContent>
        </IonModal>
      </IonContent>
    </IonPage>
  );
};

export default IzinDinas;
