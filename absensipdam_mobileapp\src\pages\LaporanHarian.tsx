import React, { useState, useEffect } from 'react';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonSegment,
  IonSegmentButton,
  IonLabel,
  IonButtons,
  IonBackButton,
  useIonToast
} from '@ionic/react';

import RiwayatLaporan from '../components/RiwayatLaporan';
import TambahLaporan from '../components/TambahLaporan';

// Style untuk header yang selaras dengan halaman lain
const headerStyle: React.CSSProperties = {
  background: 'linear-gradient(135deg, #1a65eb 0%, #1a65eb 100%)',
  minHeight: 80,
  boxShadow: 'none'
};

const titleStyle: React.CSSProperties = {
  color: '#fff',
  fontSize: '1.2rem',
  fontWeight: 'bold',
  textAlign: 'center',
  marginRight: '50px'
};

const LaporanHarian: React.FC = () => {
  const [selectedSegment, setSelectedSegment] = useState<'riwayat' | 'tambah'>('riwayat');
  const [present] = useIonToast();

  // Callback untuk refresh riwayat setelah tambah laporan
  const handleLaporanAdded = () => {
    setSelectedSegment('riwayat');
    present({
      message: 'Laporan bulanan berhasil ditambahkan!',
      color: 'success',
      duration: 3000,
      position: 'top'
    });
  };

  return (
    <IonPage>
      <IonHeader style={headerStyle}>
        <IonToolbar color="transparent" style={{ background: 'transparent', minHeight: 80, boxShadow: 'none' }}>
          <IonButtons slot="start">
            <IonBackButton
              defaultHref="/home"
              text=""
              style={{
                color: '#fff',
                fontSize: 28,
                marginLeft: 4,
                background: 'rgba(0, 0, 0, 0)',
                borderRadius: 12,
                padding: 4
              }}
            />
          </IonButtons>
          <IonTitle style={titleStyle}>Laporan Harian</IonTitle>
        </IonToolbar>
      </IonHeader>

      <IonContent fullscreen>
        {/* Segment untuk switch antara riwayat dan tambah */}
        <div style={{ padding: '16px 16px 0 16px' }}>
          <IonSegment 
            value={selectedSegment} 
            onIonChange={e => setSelectedSegment(e.detail.value as 'riwayat' | 'tambah')}
            style={{
              background: '#f8f9fa',
              borderRadius: '12px',
              padding: '4px'
            }}
          >
            <IonSegmentButton value="riwayat">
              <IonLabel style={{ fontWeight: 600 }}>📋 Riwayat</IonLabel>
            </IonSegmentButton>
            <IonSegmentButton value="tambah">
              <IonLabel style={{ fontWeight: 600 }}>➕ Tambah</IonLabel>
            </IonSegmentButton>
          </IonSegment>
        </div>

        {/* Content berdasarkan segment yang dipilih */}
        <div style={{ padding: '16px' }}>
          {selectedSegment === 'riwayat' && (
            <RiwayatLaporan />
          )}
          
          {selectedSegment === 'tambah' && (
            <TambahLaporan onLaporanAdded={handleLaporanAdded} />
          )}
        </div>
      </IonContent>
    </IonPage>
  );
};

export default LaporanHarian;
