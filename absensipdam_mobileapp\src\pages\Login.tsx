import React, { useState } from 'react';
import {
  IonPage, IonContent, IonInput, IonItem, IonLabel, IonButton, IonToast, IonLoading, IonCard, IonCardHeader, IonCardTitle, IonCardContent, IonIcon, IonText, IonList
} from '@ionic/react';
import { personCircleOutline, lockClosedOutline, logInOutline, eyeOutline, eyeOffOutline } from 'ionicons/icons';
import { useHistory } from 'react-router-dom';
import './Login.css';
import { AuthService, StorageService } from '../config/api';

const Login: React.FC = () => {
  const [nik, setNik] = useState('');
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const history = useHistory();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!nik.trim()) {
      setToastMessage('NIK tidak boleh kosong');
      setShowToast(true);
      return;
    }

    setLoading(true);

    try {
      // Verify NIK first
      const verifyResponse = await AuthService.verify(nik);

      if (!verifyResponse.success) {
        setToastMessage(verifyResponse.message || 'NIK tidak valid');
        setShowToast(true);
        setLoading(false);
        return;
      }

      // Login with NIK
      const loginResponse = await AuthService.login(nik);

      if (loginResponse.success && loginResponse.data) {
        // Save user data
        StorageService.saveUser(loginResponse.data.employee);

        setToastMessage('Login berhasil!');
        setShowToast(true);

        // Redirect to home
        setTimeout(() => {
          history.push('/home');
        }, 1000);
      } else {
        setToastMessage(loginResponse.message || 'Login gagal');
        setShowToast(true);
      }
    } catch (error) {
      console.error('Login error:', error);
      setToastMessage('Terjadi kesalahan koneksi');
      setShowToast(true);
    } finally {
      setLoading(false);
    }
  };

  return (
    <IonPage>
      <IonContent className="ion-padding login-bg" fullscreen>
        <div className="login-hero">
          <div className="login-blob login-blob-1" />
          <div className="login-blob login-blob-2" />
          <div className="login-brand">
            <img src="/logo192.png" alt="Logo" className="login-logo" />
            <div>
              <h1 className="login-title">Absensi Karyawan</h1>
              <p className="login-desc">Masuk untuk melanjutkan aktivitas Anda</p>
            </div>
          </div>
        </div>

        <div className="login-container">
          <IonCard className="login-card glass">
            <IonCardHeader>
              <IonCardTitle className="login-card-title">
                <IonIcon icon={logInOutline} className="login-card-title-icon" />
                Masuk Akun
              </IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <form onSubmit={handleLogin} autoComplete="off">
                <IonList lines="none" className="login-list">
                  <IonItem className="login-input-item input-with-icon">
                    <IonIcon icon={personCircleOutline} slot="start" className="login-input-icon" />
                    <div className="stacked-field">
                      <IonLabel position="stacked">NIK Karyawan</IonLabel>
                      <IonInput
                        value={nik}
                        onIonChange={e => setNik(e.detail.value || '')}
                        required
                        inputmode="numeric"
                        placeholder="Masukkan NIK Anda"
                        maxlength={20}
                      />
                    </div>
                  </IonItem>

                  <IonText className="login-info">
                    <p>
                      <IonIcon icon={lockClosedOutline} style={{ marginRight: '8px' }} />
                      Sistem absensi PDAM menggunakan NIK sebagai identitas login.
                      Pastikan NIK yang dimasukkan sesuai dengan data kepegawaian.
                    </p>
                  </IonText>

                </IonList>

                <IonButton
                  expand="block"
                  type="submit"
                  className="ion-margin-top login-btn primary"
                  color="primary"
                  shape="round"
                  disabled={loading || !nik.trim()}
                >
                  <IonIcon icon={logInOutline} slot="start" />
                  {loading ? 'Memverifikasi...' : 'Masuk'}
                </IonButton>

                <div className="login-footnote">
                  <IonIcon icon={lockClosedOutline} />
                  <span>Sistem Absensi PDAM - Data Anda aman dan terenkripsi.</span>
                </div>
              </form>
            </IonCardContent>
          </IonCard>
        </div>

        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          duration={2000}
          color="danger"
        />
        <IonLoading isOpen={loading} message="Memproses..." />
      </IonContent>
    </IonPage>
  );
};

export default Login; 