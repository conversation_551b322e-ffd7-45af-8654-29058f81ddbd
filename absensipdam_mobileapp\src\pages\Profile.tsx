import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonCard,
  IonCardHeader,
  IonCardSubtitle,
  IonCardTitle,
  IonCardContent,
  IonAvatar,
  IonItem,
  IonLabel,
  IonSpinner,
  IonButton,
  useIonRouter,
  IonList,
  IonIcon,
  IonChip,
  IonSkeletonText,
  IonBadge
} from '@ionic/react';
import { useEffect, useState } from 'react';
import {
  idCardOutline,
  businessOutline,
  briefcaseOutline,
  locationOutline,
  keyOutline,
  timeOutline,
  calendarOutline
} from 'ionicons/icons';
import './Profile.css';

interface Karyawan {
  id: string;
  nik: string;
  nama: string;
  bidang_id: string;
  bidang: string;
  jabatan: string;
  foto_profil: string;
  lokasi_id: string;
  nama_lokasi: string;
  allow_flexible_schedule?: string | number;
}

const Profile: React.FC = () => {
  const [karyawan, setKaryawan] = useState<Karyawan | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useIonRouter(); // Untuk navigasi

  // Tiru penerapan pada Home: normalisasi user dari localStorage menjadi objek
  const user = (() => {
    try {
      const raw = localStorage.getItem('user');
      const parsed = raw ? JSON.parse(raw) : {};
      return Array.isArray(parsed) ? (parsed[0] || {}) : parsed;
    } catch {
      return {} as any;
    }
  })();

  useEffect(() => {
    const userData = localStorage.getItem('user');

    if (userData) {
      const raw = JSON.parse(userData);
      const user = Array.isArray(raw) ? raw[0] : raw;
      const nik = user?.nik;
      
      if (!nik) {
        setLoading(false);
        return;
      }

      fetch(`https://absensiku.trunois.my.id/api/karyawan.php?api_key=absensiku_api_key_2023&nik=${encodeURIComponent(nik)}`)
        .then(response => response.json())
        .then(data => {
          if (data.status === 'success') {
            const list = Array.isArray(data.data) ? data.data : [data.data];
            const matched = list.find((r: any) => String(r?.nik) === String(nik));

            if (matched) {
              setKaryawan(matched);

              // Selaraskan data karyawan ke localStorage.user (merge data lokal + terbaru dari API)
              try {
                // Canonicalisasi field agar konsisten di seluruh app
                const canonicalFetched = {
                  id: matched?.id ?? user?.id,
                  // Penting: jangan pernah menimpa nik lokal; gunakan nik login sebagai sumber kebenaran
                  nik: user?.nik,
                  nama: matched?.nama || matched?.nama_karyawan || matched?.name || user?.nama,
                  jabatan: matched?.jabatan ?? user?.jabatan,
                  bidang_id: matched?.bidang_id ?? user?.bidang_id,
                  bidang: matched?.bidang || matched?.nama_bidang || matched?.bidang_nama || user?.bidang,
                  foto_profil: matched?.foto_profil || matched?.foto || user?.foto_profil,
                  lokasi_id: matched?.lokasi_id ?? user?.lokasi_id,
                  nama_lokasi: matched?.nama_lokasi || matched?.lokasi || matched?.nama_lokasi_kerja || user?.nama_lokasi,
                  allow_flexible_schedule: matched?.allow_flexible_schedule ?? user?.allow_flexible_schedule,
                } as any;
                const mergedUser = { ...user, ...canonicalFetched };
                localStorage.setItem('user', JSON.stringify(mergedUser));
              } catch {}
            } else {
              // Jika tidak ada yang cocok, jangan menimpa localStorage sama sekali
              // Opsional: tetap tampilkan info dari local user
            }
          }
          setLoading(false);
        })
        .catch(error => {
          console.error('Error fetching data:', error);
          setLoading(false);
        });
    }
  }, []);

  if (loading) {
    return (
      <IonPage>
        <IonHeader>
          <IonToolbar>
            <IonTitle>Profil Karyawan</IonTitle>
          </IonToolbar>
        </IonHeader>
        <IonContent className="profile-page" fullscreen>
          <div className="profile-hero">
            <div className="avatar-wrap skeleton">
              <IonSkeletonText animated style={{ width: 120, height: 120, borderRadius: '50%' }} />
            </div>
            <div className="name-wrap">
              <IonSkeletonText animated style={{ width: '60%', height: 20, borderRadius: 8 }} />
              <IonSkeletonText animated style={{ width: '40%', height: 16, borderRadius: 8, marginTop: 8 }} />
            </div>
            <div className="chip-wrap">
              <IonSkeletonText animated style={{ width: 120, height: 28, borderRadius: 20 }} />
              <IonSkeletonText animated style={{ width: 140, height: 28, borderRadius: 20 }} />
            </div>
          </div>
          <IonCard className="profile-card glass">
            <IonCardHeader>
              <IonCardTitle>Informasi Karyawan</IonCardTitle>
              <IonCardSubtitle>Memuat data...</IonCardSubtitle>
            </IonCardHeader>
            <IonCardContent>
              <IonList lines="none" className="info-list">
                <IonItem className="info-item">
                  <IonIcon icon={idCardOutline} slot="start" className="info-icon" />
                  <IonLabel>
                    <p className="label">NIK</p>
                    <IonSkeletonText animated style={{ width: '40%', height: 16, borderRadius: 6 }} />
                  </IonLabel>
                </IonItem>
                <IonItem className="info-item">
                  <IonIcon icon={briefcaseOutline} slot="start" className="info-icon" />
                  <IonLabel>
                    <p className="label">Jabatan</p>
                    <IonSkeletonText animated style={{ width: '50%', height: 16, borderRadius: 6 }} />
                  </IonLabel>
                </IonItem>
                <IonItem className="info-item">
                  <IonIcon icon={businessOutline} slot="start" className="info-icon" />
                  <IonLabel>
                    <p className="label">Bidang</p>
                    <IonSkeletonText animated style={{ width: '35%', height: 16, borderRadius: 6 }} />
                  </IonLabel>
                </IonItem>
                <IonItem className="info-item">
                  <IonIcon icon={locationOutline} slot="start" className="info-icon" />
                  <IonLabel>
                    <p className="label">Lokasi</p>
                    <IonSkeletonText animated style={{ width: '45%', height: 16, borderRadius: 6 }} />
                  </IonLabel>
                </IonItem>
              </IonList>
              <div className="action-group">
                <IonButton expand="block" color="warning" disabled>
                  <IonIcon icon={keyOutline} slot="start" />
                  Ganti Password
                </IonButton>
              </div>
            </IonCardContent>
          </IonCard>
        </IonContent>
      </IonPage>
    );
  }

  return (
      <IonPage>
        <IonHeader>
          <IonToolbar>
            <IonTitle>Profil Karyawan</IonTitle>
          </IonToolbar>
        </IonHeader>
        <IonContent className="profile-page" fullscreen>
          {(() => {
            const display = {
              foto_profil: user?.foto_profil || karyawan?.foto_profil || (karyawan as any)?.foto || null,
              nama: user?.nama || karyawan?.nama || (karyawan as any)?.nama_karyawan || (user as any)?.name || '-',
              jabatan: user?.jabatan || karyawan?.jabatan || '-',
              bidang: user?.bidang || karyawan?.bidang || (karyawan as any)?.nama_bidang || '-',
              nama_lokasi: user?.nama_lokasi || karyawan?.nama_lokasi || (karyawan as any)?.lokasi || (karyawan as any)?.nama_lokasi_kerja || '-',
              nik: user?.nik || karyawan?.nik || '-',
            };
            return (
          <div className="profile-hero">
            <div className="avatar-wrap">
              <IonAvatar className="profile-avatar">
                {display.foto_profil ? (
                  <img
                    src={`https://absensiku.trunois.my.id/uploads/${display.foto_profil}`}
                    alt="Foto Profil"
                  />)
                  : (
                  <div className="default-avatar">
                    {(display.nama || '?').charAt(0).toUpperCase()}
                  </div>
                )}
              </IonAvatar>
            </div>
            <h2 className="profile-name">{display.nama}</h2>
            <p className="profile-role">{display.jabatan}</p>
            <div className="chip-wrap">
              <IonChip color="light" className="profile-chip">
                <IonIcon icon={businessOutline} />
                <span>{display.bidang}</span>
              </IonChip>
              <IonChip color="light" className="profile-chip">
                <IonIcon icon={locationOutline} />
                <span>{display.nama_lokasi}</span>
              </IonChip>
            </div>
          </div>
            );
          })()}

          <IonCard className="profile-card glass">
            <IonCardHeader>
              <IonCardTitle>Informasi Karyawan</IonCardTitle>
              <IonCardSubtitle>Detail singkat</IonCardSubtitle>
            </IonCardHeader>
            <IonCardContent>
              <IonList lines="none" className="info-list">
                <IonItem className="info-item">
                  <IonIcon icon={idCardOutline} slot="start" className="info-icon" />
                  <IonLabel>
                    <p className="label">NIK</p>
                    <h3 className="value">{(user?.nik || karyawan?.nik) ?? '-'}</h3>
                  </IonLabel>
                </IonItem>
                <IonItem className="info-item">
                  <IonIcon icon={briefcaseOutline} slot="start" className="info-icon" />
                  <IonLabel>
                    <p className="label">Jabatan</p>
                    <h3 className="value">{(user?.jabatan || karyawan?.jabatan) ?? '-'}</h3>
                  </IonLabel>
                </IonItem>
                <IonItem className="info-item">
                  <IonIcon icon={businessOutline} slot="start" className="info-icon" />
                  <IonLabel>
                    <p className="label">Bidang</p>
                    <h3 className="value">{(user?.bidang || karyawan?.bidang || (karyawan as any)?.nama_bidang) ?? '-'}</h3>
                  </IonLabel>
                </IonItem>
                <IonItem className="info-item">
                  <IonIcon icon={locationOutline} slot="start" className="info-icon" />
                  <IonLabel>
                    <p className="label">Lokasi</p>
                    <h3 className="value">{(user?.nama_lokasi || karyawan?.nama_lokasi || (karyawan as any)?.lokasi || (karyawan as any)?.nama_lokasi_kerja) ?? '-'}</h3>
                  </IonLabel>
                </IonItem>
              </IonList>

              <div className="action-group">
                <IonButton expand="block" color="warning" onClick={() => router.push('/ganti-password', 'forward')}>
                  <IonIcon icon={keyOutline} slot="start" />
                  Ganti Password
                </IonButton>
              </div>
            </IonCardContent>
          </IonCard>

          {/* Card Informasi Jam Kerja - Hanya tampilkan jika allow_flexible_schedule != 1 */}
          {(() => {
            const flexibleSchedule = user?.allow_flexible_schedule || karyawan?.allow_flexible_schedule;
            // Jika allow_flexible_schedule = 1, jangan tampilkan section jam kerja
            if (flexibleSchedule === 1 || flexibleSchedule === '1') {
              return null;
            }
            
            return (
              <IonCard className="profile-card glass">
                <IonCardHeader>
                  <IonCardTitle>Jadwal Kerja</IonCardTitle>
                  <IonCardSubtitle>Jam kerja karyawan</IonCardSubtitle>
                </IonCardHeader>
                <IonCardContent>
                  <IonList lines="none" className="info-list">
                    {/* Senin - Kamis */}
                    <IonItem className="info-item">
                      <IonIcon icon={calendarOutline} slot="start" className="info-icon" />
                      <IonLabel>
                        <p className="label">Senin - Kamis</p>
                        <div className="schedule-info">
                          <IonBadge color="success" className="schedule-badge">
                            <IonIcon icon={timeOutline} slot="start" />
                            Masuk: 07:02
                          </IonBadge>
                          <IonBadge color="danger" className="schedule-badge">
                            <IonIcon icon={timeOutline} slot="start" />
                            Pulang: 15:30
                          </IonBadge>
                        </div>
                      </IonLabel>
                    </IonItem>
                    
                    {/* Jumat */}
                    <IonItem className="info-item">
                      <IonIcon icon={calendarOutline} slot="start" className="info-icon" />
                      <IonLabel>
                        <p className="label">Jumat</p>
                        <div className="schedule-info">
                          <IonBadge color="success" className="schedule-badge">
                            <IonIcon icon={timeOutline} slot="start" />
                            Masuk: 06:30
                          </IonBadge>
                          <IonBadge color="danger" className="schedule-badge">
                            <IonIcon icon={timeOutline} slot="start" />
                            Pulang: 11:30
                          </IonBadge>
                        </div>
                      </IonLabel>
                    </IonItem>
                    
                    {/* Sabtu - Minggu */}
                    <IonItem className="info-item">
                      <IonIcon icon={calendarOutline} slot="start" className="info-icon" />
                      <IonLabel>
                        <p className="label">Sabtu - Minggu</p>
                        <div className="schedule-info">
                          <IonBadge color="medium" className="schedule-badge">
                            <IonIcon icon={timeOutline} slot="start" />
                            Libur
                          </IonBadge>
                        </div>
                      </IonLabel>
                    </IonItem>
                  </IonList>
                </IonCardContent>
              </IonCard>
            );
          })()}
        </IonContent>
      </IonPage>
  );
};

export default Profile;
