/* Background konsisten dengan Home */
ion-content.rapat-content {
  --background: linear-gradient(135deg, #f0fdfa 0%, #e0e7ff 100%);
}



.rapat-container {
  padding: 16px;
  padding-bottom: 32px;
  max-width: 420px;
  margin: 0 auto;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  gap: 16px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  text-align: center;
  color: var(--ion-color-medium);
  gap: 16px;
}

.empty-state ion-icon {
  font-size: 64px;
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.empty-state p {
  margin: 0;
  font-size: 0.9rem;
}

.rapat-card {
  margin-bottom: 16px;
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.07);
  background: #fff;
  border: none;
}

.rapat-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
}

.badge-container {
  display: flex;
  flex-direction: column;
  gap: 6px;
  align-items: flex-end;
}

.rapat-header ion-card-title {
  flex: 1;
  font-size: 1.1rem;
  font-weight: 600;
  line-height: 1.3;
}

.status-badge {
  flex-shrink: 0;
  font-size: 0.85rem; /* Diperbesar dari 0.75rem */
  padding: 6px 12px; /* Padding diperbesar juga */
  border-radius: 12px;
  font-weight: 600; /* Font weight ditambahkan */
}

.rapat-details {
  margin-top: 8px;
}

.detail-item {
  --padding-start: 0;
  --padding-end: 0;
  --inner-padding-end: 0;
  margin-bottom: 8px;
}

.detail-item ion-icon {
  color: var(--ion-color-primary);
  margin-right: 12px;
  font-size: 1.1rem;
}

.detail-item ion-label h3 {
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--ion-color-medium);
  margin: 0 0 2px 0;
}

.detail-item ion-label p {
  font-size: 0.95rem;
  color: var(--ion-color-dark);
  margin: 0;
  font-weight: 500;
}

.scan-button {
  margin-top: 16px;
  --border-radius: 12px;
  font-weight: 600;
  --background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  --color: white;
  height: 48px;
  font-size: 1rem;
}

.not-registered {
  margin-top: 16px;
  text-align: center;
  font-style: italic;
}

.not-registered p {
  margin: 0;
  font-size: 0.9rem;
}

/* Scanner Modal Styles */
.scanner-modal-content {
  --background: #f5f5f5;
}

.scanner-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  justify-content: space-between;
}

.scanner-preview {
  width: 100%;
  max-width: 350px;
  height: 350px;
  background: #000;
  border-radius: 16px;
  position: relative;
  overflow: hidden;
  margin: 20px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scanner-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 16px;
}

.scanner-canvas {
  position: absolute;
  top: 0;
  left: 0;
}

.scanner-frame {
  width: 280px;
  height: 280px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  z-index: 10;
}

.scanner-corners {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.corner {
  position: absolute;
  width: 30px;
  height: 30px;
  border: 3px solid #00ff00;
}

.corner.top-left {
  top: 10px;
  left: 10px;
  border-right: none;
  border-bottom: none;
  border-radius: 8px 0 0 0;
}

.corner.top-right {
  top: 10px;
  right: 10px;
  border-left: none;
  border-bottom: none;
  border-radius: 0 8px 0 0;
}

.corner.bottom-left {
  bottom: 10px;
  left: 10px;
  border-right: none;
  border-top: none;
  border-radius: 0 0 0 8px;
}

.corner.bottom-right {
  bottom: 10px;
  right: 10px;
  border-left: none;
  border-top: none;
  border-radius: 0 0 8px 0;
}

.scanner-instructions {
  text-align: center;
  margin: 20px 0;
  padding: 0 20px;
}

.scanner-instructions h3 {
  color: #333;
  margin-bottom: 8px;
  font-size: 18px;
  font-weight: 600;
}

.scanner-instructions p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.scanner-actions {
  width: 100%;
  max-width: 300px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: auto;
  padding-bottom: 20px;
}

/* Scanner active state for modal - limited to preview area */
.scanner-modal-content.scanner-active {
  --background: #f5f5f5;
}

.scanner-modal-content.scanner-active .scanner-preview {
  background: transparent !important;
}

/* Hide everything except the scanner preview when scanning */
.scanner-modal-content.scanner-active .scanner-instructions,
.scanner-modal-content.scanner-active .scanner-actions {
  opacity: 0.7;
}

/* Ensure scanner only appears in preview area */
.scanner-preview.scanning {
  position: relative;
  overflow: hidden;
}

.scanner-preview.scanning::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 1;
}

/* Scanning indicator animation */
.scanning-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
}

.scan-line {
  position: absolute;
  left: 20px;
  right: 20px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #00ff00, transparent);
  animation: scan 2s linear infinite;
}

@keyframes scan {
  0% {
    top: 20px;
    opacity: 1;
  }
  50% {
    opacity: 1;
  }
  100% {
    top: calc(100% - 22px);
    opacity: 0;
  }
}

/* Active scanning state */
.scanner-preview.scanning .corner {
  border-color: #00ff00;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .rapat-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
  
  .detail-item ion-label p {
    color: var(--ion-color-light);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .rapat-container {
    padding: 12px;
  }
  
  .rapat-card {
    margin-bottom: 12px;
  }
  
  .rapat-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .status-badge {
    align-self: flex-start;
  }
}

/* Animation for loading */
.loading-container ion-spinner {
  --color: var(--ion-color-primary);
}

/* Hover effects for interactive elements */
.scan-button:hover {
  transform: translateY(-1px);
  transition: transform 0.2s ease;
}

/* Status badge colors */
ion-badge[color="success"] {
  --background: var(--ion-color-success);
  --color: white;
}

ion-badge[color="medium"] {
  --background: var(--ion-color-medium);
  --color: white;
}

/* Card hover effect */
.rapat-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.rapat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* Refresh indicator styling */
ion-refresher-content {
  --color: var(--ion-color-primary);
}

/* Header styling */
ion-header ion-toolbar {
  --background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  --color: white;
}

ion-header ion-title {
  font-weight: 600;
  font-size: 1.2rem;
}

ion-header ion-button {
  --color: white;
}

/* Loading state improvements */
.loading-container {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  margin: 20px;
  padding: 40px 20px;
}

/* Empty state improvements */
.empty-state {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  margin: 20px;
  padding: 40px 20px;
}

/* Toast customization */
ion-toast {
  --border-radius: 12px;
}

/* Alert customization */
ion-alert {
  --border-radius: 12px;
}

/* Active rapat styling */
.active-rapat {
  border-left: 4px solid var(--ion-color-warning);
  box-shadow: 0 4px 24px rgba(245, 158, 11, 0.15);
}

.active-rapat ion-card-title {
  color: var(--ion-color-warning-shade);
}

/* Badge warning color */
ion-badge[color="warning"] {
  --background: var(--ion-color-warning);
  --color: white;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}
