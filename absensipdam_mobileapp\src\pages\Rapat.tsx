import React, { useEffect, useState, useRef } from 'react';
import {
  IonContent, IonPage, IonHeader, IonToolbar, IonTitle, IonButtons, IonBackButton,
  IonCard, IonCardContent, IonCardHeader, IonCardTitle, IonItem, IonLabel,
  IonButton, IonIcon, IonSpinner, IonText, IonBadge, IonRefresher, IonRefresherContent,
  IonAlert, IonToast, IonModal
} from '@ionic/react';
import {
  qrCodeOutline, calendarOutline, timeOutline, locationOutline,
  peopleOutline, checkmarkCircleOutline, closeCircleOutline
} from 'ionicons/icons';
import { BrowserMultiFormatReader, NotFoundException } from '@zxing/library';
import { RapatService, RapatData, RapatPesertaData } from '../utils/rapatService';
import './Rapat.css';

const Rapat: React.FC = () => {
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const [rapatList, setRapatList] = useState<RapatData[]>([]);
  const [rapatPesertaList, setRapatPesertaList] = useState<RapatPesertaData[]>([]);
  const [loading, setLoading] = useState(false);
  const [scanning, setScanning] = useState(false);
  const [showScannerModal, setShowScannerModal] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [codeReader, setCodeReader] = useState<BrowserMultiFormatReader | null>(null);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastColor, setToastColor] = useState<'success' | 'danger' | 'warning'>('success');
  const [refreshing, setRefreshing] = useState(false);

  // Fetch data rapat
  const fetchRapatData = async () => {
    setLoading(true);
    try {
      const data = await RapatService.getAllRapat();
      setRapatList(data);
    } catch (error) {
      console.error('Error fetching rapat data:', error);
      setRapatList([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch data rapat peserta
  const fetchRapatPesertaData = async () => {
    try {
      const userId = user.id || user.nik;
      const data = await RapatService.getRapatPesertaByUserId(userId);
      setRapatPesertaList(data);
    } catch (error) {
      console.error('Error fetching rapat peserta data:', error);
      setRapatPesertaList([]);
    }
  };

  // Cek status kehadiran user untuk rapat tertentu
  const getStatusKehadiran = (rapatId: string) => {
    const userId = user.id || user.nik;
    const peserta = rapatPesertaList.find(p =>
      p.rapat_id == rapatId && p.user_id == userId // Gunakan == untuk type coercion
    );
    console.log(`Checking status for rapat ${rapatId}, user ${userId}:`, peserta);
    return peserta ? peserta.status : null;
  };

  // Format tanggal dan waktu menggunakan service
  const formatTanggal = (tanggal: string) => RapatService.formatTanggal(tanggal);
  const formatWaktu = (waktu: string) => RapatService.formatWaktu(waktu);

  // Open scanner modal
  const openScannerModal = async () => {
    try {
      setShowScannerModal(true);
    } catch (error) {
      console.error('Error opening scanner modal:', error);
      setToastMessage('Gagal membuka scanner');
      setToastColor('danger');
      setShowToast(true);
    }
  };

  // Initialize ZXing code reader
  useEffect(() => {
    const reader = new BrowserMultiFormatReader();
    setCodeReader(reader);

    return () => {
      reader.reset();
    };
  }, []);

  // Start camera when modal opens
  useEffect(() => {
    if (showScannerModal && codeReader) {
      startScanning();
    } else {
      stopScanning();
    }

    return () => {
      stopScanning();
    };
  }, [showScannerModal, codeReader]);

  // Start scanning with ZXing
  const startScanning = async () => {
    if (!codeReader || !videoRef.current) {
      console.log('CodeReader or video ref not available');
      return;
    }

    try {
      console.log('Starting ZXing scanner...');
      setScanning(true);

      // Start decoding from video element
      const result = await codeReader.decodeOnceFromVideoDevice(undefined, videoRef.current);

      if (result) {
        console.log('Barcode detected:', result.getText());
        setScanning(false);
        await handleBarcodeResult(result.getText());
        setShowScannerModal(false);
      }
    } catch (error) {
      if (error instanceof NotFoundException) {
        console.log('No barcode found, retrying...');
        // Retry scanning
        if (scanning) {
          setTimeout(() => {
            startScanning();
          }, 100);
        }
      } else {
        console.error('Error scanning barcode:', error);
        setToastMessage('Gagal melakukan scan barcode');
        setToastColor('danger');
        setShowToast(true);
        setScanning(false);
      }
    }
  };

  // Stop scanning
  const stopScanning = () => {
    if (codeReader) {
      codeReader.reset();
    }
    setScanning(false);
  };

  // Close scanner modal
  const closeScannerModal = () => {
    setShowScannerModal(false);
    stopScanning();
  };

  // Cek apakah scan sudah tidak diperbolehkan (melewati tanggal rapat atau lewat waktu selesai pada tanggal yang sama)
  const isAfterMeetingEndTime = (rapat: RapatData) => {
    const now = new Date();
    const todayStr = now.toISOString().split('T')[0]; // YYYY-MM-DD

    // Jika tanggal rapat sudah lewat dari hari ini, anggap waktu absensi sudah berakhir
    if (rapat.tanggal && rapat.tanggal < todayStr) return true;

    // Jika tanggal rapat masih di depan, belum berakhir
    if (rapat.tanggal && rapat.tanggal > todayStr) return false;

    // Tanggal sama dengan hari ini: cek terhadap waktu_selesai jika ada
    if (!rapat.waktu_selesai) return false;
    const endDateTime = new Date(`${todayStr}T${rapat.waktu_selesai}`);
    return now > endDateTime;
  };

  // Handle hasil scan barcode
  const handleBarcodeResult = async (barcodeValue: string) => {
    try {
      const userId = user.id || user.nik;
      const result = await RapatService.processRapatAbsensi(barcodeValue, userId);

      if (result.success) {
        setToastMessage(result.message);
        setToastColor('success');
        setShowToast(true);

        // Refresh data peserta dengan delay untuk memastikan server sudah update
        console.log('Refreshing data after successful update...');

        // Clear cache dan refresh data
        localStorage.removeItem('rapat_peserta_cache');

        setTimeout(async () => {
          await fetchRapatPesertaData();
          console.log('Data refreshed, new peserta list:', rapatPesertaList);

          // Force re-render dengan update state
          setRapatPesertaList(prev => [...prev]);
        }, 1000);
      } else {
        setToastMessage(result.message);
        setToastColor(result.message.includes('tidak terdaftar') ? 'warning' : 'danger');
        setShowToast(true);
      }
    } catch (error) {
      console.error('Error handling barcode result:', error);
      setToastMessage('Terjadi kesalahan saat memproses barcode');
      setToastColor('danger');
      setShowToast(true);
    }
  };



  // Refresh data
  const handleRefresh = async (event: CustomEvent) => {
    setRefreshing(true);
    try {
      await Promise.all([fetchRapatData(), fetchRapatPesertaData()]);
      // Simpan data ke localStorage untuk offline access
      await RapatService.saveRapatToLocalStorage();
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
      event.detail.complete();
    }
  };



  useEffect(() => {
    fetchRapatData();
    fetchRapatPesertaData();
  }, []);

  // Header style selaras dengan halaman lain
  const headerStyle = {
    background: 'linear-gradient(135deg, #1a65eb 0%, #1a65eb 100%)',
    minHeight: '80px'
  };

  const titleStyle = {
    color: '#fff',
    fontWeight: '600',
    fontSize: '1.2rem'
  };

  return (
    <IonPage>
      <IonHeader style={headerStyle}>
        <IonToolbar color="transparent" style={{ background: 'transparent', minHeight: 80, boxShadow: 'none' }}>
          <IonButtons slot="start">
            <IonBackButton defaultHref="/home" text="" style={{ color: '#fff', fontSize: 28, marginLeft: 4, background: 'rgba(0, 0, 0, 0)', borderRadius: 12, padding: 4 }} />
          </IonButtons>
          <IonTitle style={titleStyle}>Rapat</IonTitle>
          <IonButtons slot="end">
            <IonButton onClick={openScannerModal} disabled={scanning} style={{ color: '#fff' }}>
              <IonIcon icon={qrCodeOutline} />
            </IonButton>
          </IonButtons>
        </IonToolbar>
      </IonHeader>

      <IonContent fullscreen className="rapat-content">
        <IonRefresher slot="fixed" onIonRefresh={handleRefresh}>
          <IonRefresherContent></IonRefresherContent>
        </IonRefresher>

        {loading ? (
          <div className="loading-container">
            <IonSpinner name="crescent" />
            <IonText>Memuat data rapat...</IonText>
          </div>
        ) : (
          <div className="rapat-container">
            {rapatList.length === 0 ? (
              <div className="empty-state">
                <IonIcon icon={peopleOutline} size="large" />
                <IonText>
                  <h3>Tidak ada rapat</h3>
                  <p>Belum ada rapat yang tersedia saat ini</p>
                </IonText>
              </div>
            ) : (
              rapatList
                .filter((rapat) => {
                  const userId = user.id || user.nik;
                  const isRegistered = rapatPesertaList.some(p =>
                    p.rapat_id == rapat.id && p.user_id == userId
                  );
                  return isRegistered; // Hanya tampilkan rapat jika user terdaftar sebagai peserta
                })
                .map((rapat) => {
                const userId = user.id || user.nik;
                const statusKehadiran = getStatusKehadiran(rapat.id);
                const isRegistered = rapatPesertaList.some(p =>
                  p.rapat_id == rapat.id && p.user_id == userId // Gunakan == dan cek user_id juga
                );
                const isActive = RapatService.isRapatActive(rapat);

                console.log(`Rapat ${rapat.id}: registered=${isRegistered}, status=${statusKehadiran}`);

                return (
                  <IonCard key={rapat.id} className={`rapat-card ${isActive ? 'active-rapat' : ''}`}>
                    <IonCardHeader>
                      <div className="rapat-header">
                        <IonCardTitle>{rapat.judul}</IonCardTitle>
                        <div className="badge-container">
                          {isActive && (
                            <IonBadge color="warning" className="status-badge">
                              Sedang Berlangsung
                            </IonBadge>
                          )}
                          {isRegistered && (
                            <IonBadge
                              color={statusKehadiran === 'hadir' ? 'success' : 'medium'}
                              className="status-badge"
                            >
                              {statusKehadiran === 'hadir' ? 'Hadir' : 'Belum Absen'}
                            </IonBadge>
                          )}
                        </div>
                      </div>
                    </IonCardHeader>
                    
                    <IonCardContent>
                      <div className="rapat-details">
                        <IonItem lines="none" className="detail-item">
                          <IonIcon icon={calendarOutline} slot="start" />
                          <IonLabel>
                            <h3>Tanggal</h3>
                            <p>{formatTanggal(rapat.tanggal)}</p>
                          </IonLabel>
                        </IonItem>
                        
                        <IonItem lines="none" className="detail-item">
                          <IonIcon icon={timeOutline} slot="start" />
                          <IonLabel>
                            <h3>Waktu</h3>
                            <p>{formatWaktu(rapat.waktu_mulai)} - {formatWaktu(rapat.waktu_selesai)}</p>
                          </IonLabel>
                        </IonItem>
                        
                        <IonItem lines="none" className="detail-item">
                          <IonIcon icon={locationOutline} slot="start" />
                          <IonLabel>
                            <h3>Lokasi</h3>
                            <p>{rapat.lokasi}</p>
                          </IonLabel>
                        </IonItem>
                        
                        {rapat.deskripsi && (
                          <IonItem lines="none" className="detail-item">
                            <IonLabel>
                              <h3>Deskripsi</h3>
                              <p>{rapat.deskripsi}</p>
                            </IonLabel>
                          </IonItem>
                        )}
                      </div>
                      
                      {isRegistered && statusKehadiran !== 'hadir' && !isAfterMeetingEndTime(rapat) && (
                        <IonButton
                          expand="block"
                          onClick={openScannerModal}
                          disabled={scanning}
                          className="scan-button"
                        >
                          <IonIcon icon={qrCodeOutline} slot="start" />
                          {scanning ? 'Scanning...' : 'Scan Barcode Absensi'}
                        </IonButton>
                      )}

                      {isRegistered && statusKehadiran !== 'hadir' && isAfterMeetingEndTime(rapat) && (
                        <IonText color="medium" className="time-expired">
                          <p>Waktu absensi telah berakhir</p>
                        </IonText>
                      )}
                    </IonCardContent>
                  </IonCard>
                );
              })
            )}
          </div>
        )}

        <IonAlert
          isOpen={showAlert}
          onDidDismiss={() => setShowAlert(false)}
          header="Informasi"
          message={alertMessage}
          buttons={['OK']}
        />

        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          duration={3000}
          color={toastColor}
        />
      </IonContent>

      {/* Scanner Modal */}
      <IonModal isOpen={showScannerModal} onDidDismiss={closeScannerModal}>
        <IonHeader>
          <IonToolbar>
            <IonTitle>Scan Barcode Rapat</IonTitle>
            <IonButtons slot="end">
              <IonButton onClick={closeScannerModal}>
                <IonIcon icon={closeCircleOutline} />
              </IonButton>
            </IonButtons>
          </IonToolbar>
        </IonHeader>
        <IonContent className={`scanner-modal-content ${scanning ? 'scanner-active' : ''}`}>
          <div className="scanner-container">
            <div className={`scanner-preview ${scanning ? 'scanning' : ''}`} id="scanner-preview">
              <video
                ref={videoRef}
                className="scanner-video"
                autoPlay
                playsInline
                muted
              />
              <div className="scanner-frame">
                <div className="scanner-corners">
                  <div className="corner top-left"></div>
                  <div className="corner top-right"></div>
                  <div className="corner bottom-left"></div>
                  <div className="corner bottom-right"></div>
                </div>
                {scanning && (
                  <div className="scanning-indicator">
                    <div className="scan-line"></div>
                  </div>
                )}
              </div>
            </div>
            <div className="scanner-instructions">
              <IonText>
                <h3>{scanning ? 'Scanning...' : 'Arahkan kamera ke barcode rapat'}</h3>
                <p>{scanning ? 'Pastikan barcode berada di dalam kotak dan terlihat jelas' : 'Kamera akan aktif secara otomatis setelah menekan tombol mulai scan'}</p>
              </IonText>
            </div>
            <div className="scanner-actions">
              {!scanning && (
                <IonButton
                  expand="block"
                  onClick={() => {
                    startScanning();
                  }}
                  color="primary"
                >
                  <IonIcon icon={qrCodeOutline} slot="start" />
                  Mulai Scan
                </IonButton>
              )}
              <IonButton
                expand="block"
                fill="outline"
                onClick={closeScannerModal}
                color="medium"
              >
                <IonIcon icon={closeCircleOutline} slot="start" />
                Tutup Scanner
              </IonButton>
            </div>
          </div>
        </IonContent>
      </IonModal>
    </IonPage>
  );
};

export default Rapat;
