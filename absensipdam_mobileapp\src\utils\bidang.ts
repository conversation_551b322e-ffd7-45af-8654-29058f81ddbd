import { AuthService, StorageService } from '../config/api';

export async function fetchAndStoreBidang() {
  try {
    const user = StorageService.getUser();
    if (!user?.nik) return;

    // Ambil profile user yang sudah include department info
    const response = await AuthService.getProfile(user.nik);

    if (response.success && response.data) {
      const profile = response.data;

      // Extract department/bidang info dari profile
      const bidangData = {
        id: profile.department_id,
        nama: profile.department_name,
        kode: profile.department_code,
        kepala_bidang: profile.department_head,
        lokasi_id: profile.location_id,
        is_active: true
      };

      localStorage.setItem('bidang_current', JSON.stringify(bidangData));

      // Untuk backward compatibility
      localStorage.setItem('bidang_list', JSON.stringify([bidangData]));
    }
  } catch (err) {
    console.error('Error fetching bidang from PDAM API:', err);
  }
}

// Fungsi untuk mendapatkan bidang user saat ini
export function getCurrentBidang() {
  try {
    return JSON.parse(localStorage.getItem('bidang_current') || '{}');
  } catch {
    return {};
  }
}

// Fungsi untuk mendapatkan nama bidang
export function getBidangName() {
  try {
    const bidang = getCurrentBidang();
    return bidang.nama || 'Tidak Diketahui';
  } catch {
    return 'Tidak Diketahui';
  }
}