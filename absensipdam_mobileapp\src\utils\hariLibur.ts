export async function fetchAndStoreHariLibur() {
  try {
    const res = await fetch('https://absensiku.trunois.my.id/api/hari_libur.php?api_key=absensiku_api_key_2023');
    const data = await res.json();
    if (data.status === 'success' && Array.isArray(data.data)) {
      localStorage.setItem('hari_libur_list', JSON.stringify(data.data));
    }
  } catch (err) {
    // Optional: handle error
  }
}

export function isTodayLibur(): { libur: boolean; nama?: string } {
  const list = JSON.parse(localStorage.getItem('hari_libur_list') || '[]');
  const today = new Date();
  const todayStr = today.toISOString().slice(0, 10); // yyyy-mm-dd
  const found = list.find((item: any) => item.tanggal === todayStr);
  if (found) {
    return { libur: true, nama: found.nama_libur };
  }
  return { libur: false };
} 