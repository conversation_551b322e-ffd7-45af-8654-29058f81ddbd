import { ScheduleService, StorageService } from '../config/api';

export async function fetchAndStoreJamKerja() {
  try {
    const user = StorageService.getUser();
    if (!user?.nik) return;

    // Ambil jadwal kerja user dari API PDAM
    const response = await ScheduleService.getCurrentSchedule(user.nik);

    if (response.success && response.data) {
      const scheduleData = response.data;
      const schedule = scheduleData.schedule;

      // Convert format API PDAM ke format yang diharapkan aplikasi
      const jamKerjaData = {
        id: schedule.id,
        nama: schedule.name,
        jam_masuk: schedule.start_time,
        jam_pulang: schedule.end_time,
        toleransi_masuk: schedule.late_check_in_limit || 15, // default 15 menit
        toleransi_pulang: schedule.early_check_out_limit || 15,
        is_flexible: false, // PDAM tidak menggunakan flexible schedule
        is_night_shift: schedule.is_cross_day || false,
        days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'] // default weekdays
      };

      localStorage.setItem('jam_kerja_current', JSON.stringify(jamKerjaData));

      // Untuk backward compatibility, simpan juga dalam format lama
      localStorage.setItem('jam_kerja_list', JSON.stringify([jamKerjaData]));
    }
  } catch (err) {
    console.error('Error fetching jam kerja from PDAM API:', err);
  }
}

// Fungsi untuk mendapatkan jadwal kerja bulanan
export async function fetchAndStoreJamKerjaBulanan() {
  try {
    const user = StorageService.getUser();
    if (!user?.nik) return;

    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth() + 1;

    const response = await ScheduleService.getMonthlySchedule(user.nik, year, month);

    if (response.success && Array.isArray(response.data)) {
      // Convert format API PDAM ke format yang diharapkan aplikasi
      const monthlySchedule = response.data.map((schedule: any) => ({
        date: schedule.date,
        shift_id: schedule.shift_id,
        shift_name: schedule.shift_name,
        start_time: schedule.start_time,
        end_time: schedule.end_time,
        is_flexible: schedule.is_flexible,
        is_night_shift: schedule.is_night_shift,
        is_holiday: schedule.is_holiday,
        is_weekend: schedule.is_weekend
      }));

      localStorage.setItem('jam_kerja_monthly', JSON.stringify(monthlySchedule));
    }
  } catch (err) {
    console.error('Error fetching monthly schedule from PDAM API:', err);
  }
}

// Fungsi untuk mendapatkan jam kerja hari ini
export function getJamKerjaHariIni() {
  try {
    const today = new Date().toISOString().split('T')[0];
    const monthlySchedule = JSON.parse(localStorage.getItem('jam_kerja_monthly') || '[]');

    const todaySchedule = monthlySchedule.find((s: any) => s.date === today);
    if (todaySchedule) {
      return todaySchedule;
    }

    // Fallback ke jadwal default
    return JSON.parse(localStorage.getItem('jam_kerja_current') || '{}');
  } catch {
    return {};
  }
}

// Fungsi untuk cek apakah jadwal fleksibel
export function isFlexibleSchedule() {
  try {
    const jamKerja = getJamKerjaHariIni();
    return jamKerja.is_flexible || false;
  } catch {
    return false;
  }
}