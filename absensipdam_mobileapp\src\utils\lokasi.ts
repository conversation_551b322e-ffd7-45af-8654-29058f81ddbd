import { AttendanceService, StorageService } from '../config/api';

export async function fetchAndStoreLokasi() {
  try {
    const user = StorageService.getUser();
    if (!user) return;

    // Ambil semua lokasi dari API PDAM
    const response = await AttendanceService.getLocations();

    if (response.success && Array.isArray(response.data)) {
      // Convert format API PDAM ke format yang diharapkan aplikasi
      const lokasiList = response.data.map((location: any) => ({
        id: location.id,
        nama: location.name,
        alamat: location.address,
        latitude: location.latitude,
        longitude: location.longitude,
        radius: location.radius,
        is_active: location.is_active
      }));

      localStorage.setItem('lokasi_list', JSON.stringify(lokasiList));

      // Juga simpan lokasi yang aktif saja
      const activeLokasi = lokasiList.filter((l: any) => l.is_active);
      localStorage.setItem('active_lokasi_list', JSON.stringify(activeLokasi));
    }
  } catch (err) {
    console.error('Error fetching lokasi from PDAM API:', err);
  }
}

// Fungsi untuk mendapatkan lokasi berdasarkan ID
export function getLokasiById(id: number) {
  try {
    const lokasiList = JSON.parse(localStorage.getItem('lokasi_list') || '[]');
    return lokasiList.find((l: any) => l.id === id);
  } catch {
    return null;
  }
}

// Fungsi untuk mendapatkan semua lokasi aktif
export function getActiveLokasi() {
  try {
    return JSON.parse(localStorage.getItem('active_lokasi_list') || '[]');
  } catch {
    return [];
  }
}