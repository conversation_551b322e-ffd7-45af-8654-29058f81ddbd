/**
 * API Testing Utilities
 * Utility untuk testing koneksi API PDAM
 */

import { ApiService, AuthService, AttendanceService, ScheduleService } from '../config/api';

export interface TestResult {
  test: string;
  success: boolean;
  message: string;
  data?: any;
  duration?: number;
}

export class ApiTester {
  
  /**
   * Test semua endpoint API
   */
  static async runAllTests(): Promise<TestResult[]> {
    const results: TestResult[] = [];
    
    console.log('🧪 Memulai API Testing...');
    
    // Test basic connectivity
    results.push(await this.testConnectivity());
    
    // Test authentication endpoints
    results.push(await this.testAuthVerify());
    results.push(await this.testAuthLogin());
    
    // Test attendance endpoints
    results.push(await this.testAttendanceLocations());
    results.push(await this.testAttendanceToday());
    
    // Test schedule endpoints
    results.push(await this.testScheduleCurrent());
    
    return results;
  }
  
  /**
   * Test basic connectivity
   */
  static async testConnectivity(): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      const response = await fetch('http://localhost/absensipdam/test_connection.php');
      const duration = Date.now() - startTime;
      
      if (response.ok) {
        return {
          test: 'Connectivity',
          success: true,
          message: 'Server dapat diakses',
          duration
        };
      } else {
        return {
          test: 'Connectivity',
          success: false,
          message: `HTTP ${response.status}: ${response.statusText}`,
          duration
        };
      }
    } catch (error) {
      return {
        test: 'Connectivity',
        success: false,
        message: `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        duration: Date.now() - startTime
      };
    }
  }
  
  /**
   * Test auth verify endpoint
   */
  static async testAuthVerify(): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      const response = await AuthService.verify('123456789');
      const duration = Date.now() - startTime;
      
      return {
        test: 'Auth Verify',
        success: response.success !== undefined,
        message: response.success ? 'Endpoint verify berfungsi' : response.message || 'Verify failed',
        data: response.data,
        duration
      };
    } catch (error) {
      return {
        test: 'Auth Verify',
        success: false,
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        duration: Date.now() - startTime
      };
    }
  }
  
  /**
   * Test auth login endpoint
   */
  static async testAuthLogin(): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      const response = await AuthService.login('123456789');
      const duration = Date.now() - startTime;
      
      return {
        test: 'Auth Login',
        success: response.success !== undefined,
        message: response.success ? 'Endpoint login berfungsi' : response.message || 'Login failed',
        data: response.data,
        duration
      };
    } catch (error) {
      return {
        test: 'Auth Login',
        success: false,
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        duration: Date.now() - startTime
      };
    }
  }
  
  /**
   * Test attendance locations endpoint
   */
  static async testAttendanceLocations(): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      const response = await AttendanceService.getLocations();
      const duration = Date.now() - startTime;
      
      return {
        test: 'Attendance Locations',
        success: response.success !== undefined,
        message: response.success ? 'Endpoint locations berfungsi' : response.message || 'Locations failed',
        data: response.data,
        duration
      };
    } catch (error) {
      return {
        test: 'Attendance Locations',
        success: false,
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        duration: Date.now() - startTime
      };
    }
  }
  
  /**
   * Test attendance today endpoint
   */
  static async testAttendanceToday(): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      const response = await AttendanceService.getToday('123456789');
      const duration = Date.now() - startTime;
      
      return {
        test: 'Attendance Today',
        success: response.success !== undefined,
        message: response.success ? 'Endpoint today berfungsi' : response.message || 'Today failed',
        data: response.data,
        duration
      };
    } catch (error) {
      return {
        test: 'Attendance Today',
        success: false,
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        duration: Date.now() - startTime
      };
    }
  }
  
  /**
   * Test schedule current endpoint
   */
  static async testScheduleCurrent(): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      const response = await ScheduleService.getCurrent('123456789');
      const duration = Date.now() - startTime;
      
      return {
        test: 'Schedule Current',
        success: response.success !== undefined,
        message: response.success ? 'Endpoint schedule berfungsi' : response.message || 'Schedule failed',
        data: response.data,
        duration
      };
    } catch (error) {
      return {
        test: 'Schedule Current',
        success: false,
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        duration: Date.now() - startTime
      };
    }
  }
  
  /**
   * Test photo upload simulation
   */
  static async testPhotoUpload(): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      // Create a small test image in base64
      const testPhoto = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=';
      
      const response = await AttendanceService.checkIn({
        nik: '123456789',
        latitude: -7.2575,
        longitude: 112.7521,
        photo: testPhoto
      });
      
      const duration = Date.now() - startTime;
      
      return {
        test: 'Photo Upload',
        success: response.success !== undefined,
        message: response.success ? 'Photo upload berfungsi' : response.message || 'Photo upload failed',
        data: response.data,
        duration
      };
    } catch (error) {
      return {
        test: 'Photo Upload',
        success: false,
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        duration: Date.now() - startTime
      };
    }
  }
  
  /**
   * Generate test report
   */
  static generateReport(results: TestResult[]): string {
    const totalTests = results.length;
    const passedTests = results.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    
    let report = `
📊 API Test Report
==================
Total Tests: ${totalTests}
Passed: ${passedTests}
Failed: ${failedTests}
Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%

📋 Test Details:
`;
    
    results.forEach((result, index) => {
      const status = result.success ? '✅' : '❌';
      const duration = result.duration ? ` (${result.duration}ms)` : '';
      
      report += `
${index + 1}. ${status} ${result.test}${duration}
   ${result.message}`;
      
      if (result.data) {
        report += `
   Data: ${JSON.stringify(result.data, null, 2)}`;
      }
    });
    
    report += `
    
🔧 Troubleshooting:
- Pastikan backend server running di http://localhost/absensipdam
- Cek konfigurasi database di config/database.php
- Pastikan API endpoints tersedia
- Cek CORS settings jika ada error network
`;
    
    return report;
  }
  
  /**
   * Quick health check
   */
  static async quickHealthCheck(): Promise<boolean> {
    try {
      const response = await fetch('http://localhost/absensipdam/test_connection.php');
      return response.ok;
    } catch {
      return false;
    }
  }
}

// Export untuk digunakan di komponen
export default ApiTester;
