import { AuthService, StorageService } from '../config/api';

// Interface untuk user profile PDAM
export interface UserProfile {
  nik: string;
  name: string;
  email?: string;
  phone?: string;
  position: string;
  department_id: number;
  department_name: string;
  department_code: string;
  department_head?: string;
  location_id: number;
  location_name: string;
  location_address: string;
  location_latitude: number;
  location_longitude: number;
  location_radius: number;
  hire_date: string;
  is_active: boolean;
  photo?: string;
}

// Fungsi untuk fetch dan store profile user lengkap
export async function fetchAndStoreUserProfile() {
  try {
    const user = StorageService.getUser();
    if (!user?.nik) return null;
    
    const response = await AuthService.getProfile(user.nik);
    
    if (response.success && response.data) {
      const employeeData = response.data;

      // Convert ke format UserProfile yang diharapkan
      const profile: UserProfile = {
        nik: employeeData.nik,
        name: employeeData.name,
        email: employeeData.email,
        phone: employeeData.phone,
        position: employeeData.position,
        department_id: employeeData.department_id || 0,
        department_name: employeeData.department || 'Tidak Diketahui',
        department_code: employeeData.department_code || '',
        department_head: employeeData.department_head,
        location_id: employeeData.location_id || 0,
        location_name: employeeData.location_name || 'Tidak Diketahui',
        location_address: employeeData.location_address || '',
        location_latitude: employeeData.location_latitude || 0,
        location_longitude: employeeData.location_longitude || 0,
        location_radius: employeeData.location_radius || 100,
        hire_date: employeeData.hire_date || '',
        is_active: employeeData.is_active || true,
        photo: employeeData.photo
      };

      // Simpan profile lengkap
      localStorage.setItem('user_profile', JSON.stringify(profile));

      // Update user data di localStorage dengan info terbaru
      const updatedUser = {
        ...user,
        name: profile.name,
        email: profile.email,
        phone: profile.phone,
        position: profile.position,
        department_id: profile.department_id,
        department_name: profile.department_name,
        location_id: profile.location_id,
        location_name: profile.location_name,
        hire_date: profile.hire_date,
        is_active: profile.is_active
      };

      StorageService.setUser(updatedUser);
      
      return profile;
    }
    
    return null;
  } catch (err) {
    console.error('Error fetching user profile from PDAM API:', err);
    return null;
  }
}

// Fungsi untuk mendapatkan profile user
export function getUserProfile(): UserProfile | null {
  try {
    const profile = localStorage.getItem('user_profile');
    return profile ? JSON.parse(profile) : null;
  } catch {
    return null;
  }
}

// Fungsi untuk mendapatkan nama lengkap user
export function getUserName(): string {
  try {
    const profile = getUserProfile();
    return profile?.name || 'Pengguna';
  } catch {
    return 'Pengguna';
  }
}

// Fungsi untuk mendapatkan posisi/jabatan user
export function getUserPosition(): string {
  try {
    const profile = getUserProfile();
    return profile?.position || 'Karyawan';
  } catch {
    return 'Karyawan';
  }
}

// Fungsi untuk mendapatkan department user
export function getUserDepartment(): { id: number; name: string; code: string } {
  try {
    const profile = getUserProfile();
    return {
      id: profile?.department_id || 0,
      name: profile?.department_name || 'Tidak Diketahui',
      code: profile?.department_code || ''
    };
  } catch {
    return { id: 0, name: 'Tidak Diketahui', code: '' };
  }
}

// Fungsi untuk mendapatkan lokasi kerja user
export function getUserLocation(): {
  id: number;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  radius: number;
} {
  try {
    const profile = getUserProfile();
    return {
      id: profile?.location_id || 0,
      name: profile?.location_name || 'Tidak Diketahui',
      address: profile?.location_address || '',
      latitude: profile?.location_latitude || 0,
      longitude: profile?.location_longitude || 0,
      radius: profile?.location_radius || 100
    };
  } catch {
    return {
      id: 0,
      name: 'Tidak Diketahui',
      address: '',
      latitude: 0,
      longitude: 0,
      radius: 100
    };
  }
}

// Fungsi untuk mendapatkan foto profile user
export function getUserPhoto(): string | null {
  try {
    const profile = getUserProfile();
    if (profile?.photo) {
      return `https://absensipdam.trunois.my.id/uploads/employee_photos/${profile.photo}`;
    }
    return null;
  } catch {
    return null;
  }
}

// Fungsi untuk cek apakah user aktif
export function isUserActive(): boolean {
  try {
    const profile = getUserProfile();
    return profile?.is_active || false;
  } catch {
    return false;
  }
}

// Fungsi untuk mendapatkan masa kerja user
export function getUserWorkDuration(): string {
  try {
    const profile = getUserProfile();
    if (!profile?.hire_date) return 'Tidak Diketahui';
    
    const hireDate = new Date(profile.hire_date);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - hireDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    const years = Math.floor(diffDays / 365);
    const months = Math.floor((diffDays % 365) / 30);
    
    if (years > 0) {
      return `${years} tahun ${months} bulan`;
    } else if (months > 0) {
      return `${months} bulan`;
    } else {
      return `${diffDays} hari`;
    }
  } catch {
    return 'Tidak Diketahui';
  }
}

// Fungsi untuk refresh profile data
export async function refreshUserProfile(): Promise<boolean> {
  try {
    const profile = await fetchAndStoreUserProfile();
    return profile !== null;
  } catch {
    return false;
  }
}

// Fungsi untuk clear profile data (saat logout)
export function clearUserProfile() {
  localStorage.removeItem('user_profile');
}
