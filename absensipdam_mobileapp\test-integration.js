#!/usr/bin/env node

/**
 * Script Testing Integrasi PDAM
 * Script untuk testing integrasi aplikasi mobile dengan API PDAM
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Integrasi Aplikasi Mobile dengan API PDAM\n');

// Fungsi helper
const log = (message, type = 'info') => {
  const colors = {
    info: '\x1b[36m',    // cyan
    success: '\x1b[32m', // green
    warning: '\x1b[33m', // yellow
    error: '\x1b[31m',   // red
    reset: '\x1b[0m'     // reset
  };
  console.log(`${colors[type]}${message}${colors.reset}`);
};

const checkFile = (filePath) => {
  return fs.existsSync(filePath);
};

// 1. Cek file konfigurasi API
log('1️⃣ Mengecek konfigurasi API...');

const apiConfigPath = path.join(__dirname, 'src', 'config', 'api.ts');
if (checkFile(apiConfigPath)) {
  log('✅ File konfigurasi API ditemukan', 'success');
  
  const apiConfig = fs.readFileSync(apiConfigPath, 'utf8');
  if (apiConfig.includes('absensipdam.trunois.my.id')) {
    log('✅ API URL sudah dikonfigurasi untuk PDAM', 'success');
  } else if (apiConfig.includes('localhost')) {
    log('⚠️  API masih menggunakan localhost', 'warning');
  }
} else {
  log('❌ File konfigurasi API tidak ditemukan', 'error');
}

// 2. Cek update halaman utama
log('\n2️⃣ Mengecek update halaman utama...');

const pages = [
  { name: 'Home', path: 'src/pages/Home.tsx', checks: ['AttendanceService', 'StorageService', 'getUserProfile', 'getUserLocation'] },
  { name: 'Absensi', path: 'src/pages/Absensi.tsx', checks: ['AttendanceService.checkIn', 'AttendanceService.checkOut', 'getJamKerjaHariIni', 'getUserLocation'] },
  { name: 'Histori', path: 'src/pages/Histori.tsx', checks: ['AttendanceService.getHistory', 'StorageService.getUser'] },
  { name: 'Login', path: 'src/pages/Login.tsx', checks: ['AuthService.login', 'AuthService.verify'] }
];

pages.forEach(page => {
  const filePath = path.join(__dirname, page.path);
  if (checkFile(filePath)) {
    log(`✅ ${page.name} page ditemukan`, 'success');
    
    const content = fs.readFileSync(filePath, 'utf8');
    let allChecksPass = true;
    
    page.checks.forEach(check => {
      if (content.includes(check)) {
        log(`  ✅ ${check} terintegrasi`, 'success');
      } else {
        log(`  ❌ ${check} belum terintegrasi`, 'error');
        allChecksPass = false;
      }
    });
    
    if (allChecksPass) {
      log(`  🎉 ${page.name} page fully integrated!`, 'success');
    }
  } else {
    log(`❌ ${page.name} page tidak ditemukan`, 'error');
  }
});

// 3. Cek utility files yang diupdate
log('\n3️⃣ Mengecek utility files...');

const utilityFiles = [
  { name: 'lokasi.ts', path: 'src/utils/lokasi.ts', checks: ['AttendanceService.getLocations', 'getUserLocation'] },
  { name: 'jamKerja.ts', path: 'src/utils/jamKerja.ts', checks: ['ScheduleService.getCurrentSchedule', 'getJamKerjaHariIni'] },
  { name: 'bidang.ts', path: 'src/utils/bidang.ts', checks: ['AuthService.getProfile', 'getCurrentBidang'] },
  { name: 'userProfile.ts', path: 'src/utils/userProfile.ts', checks: ['fetchAndStoreUserProfile', 'getUserProfile'] }
];

utilityFiles.forEach(util => {
  const filePath = path.join(__dirname, util.path);
  if (checkFile(filePath)) {
    log(`✅ ${util.name} ditemukan`, 'success');

    const content = fs.readFileSync(filePath, 'utf8');
    let allChecksPass = true;

    util.checks.forEach(check => {
      if (content.includes(check)) {
        log(`  ✅ ${check} terintegrasi`, 'success');
      } else {
        log(`  ❌ ${check} belum terintegrasi`, 'error');
        allChecksPass = false;
      }
    });

    if (allChecksPass) {
      log(`  🎉 ${util.name} fully updated!`, 'success');
    }
  } else {
    log(`❌ ${util.name} tidak ditemukan`, 'error');
  }
});

// 4. Cek halaman testing
log('\n4️⃣ Mengecek halaman testing...');

const testPages = [
  'src/pages/ApiTest.tsx',
  'src/utils/testApi.ts'
];

testPages.forEach(testPage => {
  const filePath = path.join(__dirname, testPage);
  if (checkFile(filePath)) {
    log(`✅ ${path.basename(testPage)} ditemukan`, 'success');
  } else {
    log(`❌ ${path.basename(testPage)} tidak ditemukan`, 'error');
  }
});

// 5. Cek dokumentasi
log('\n5️⃣ Mengecek dokumentasi...');

const docs = [
  'PDAM_INTEGRATION.md',
  'README_PDAM.md',
  'INTEGRATION_UPDATE.md'
];

docs.forEach(doc => {
  const filePath = path.join(__dirname, doc);
  if (checkFile(filePath)) {
    log(`✅ ${doc} ditemukan`, 'success');
  } else {
    log(`❌ ${doc} tidak ditemukan`, 'error');
  }
});

// 6. Cek App.tsx routing
log('\n6️⃣ Mengecek routing...');

const appPath = path.join(__dirname, 'src', 'App.tsx');
if (checkFile(appPath)) {
  const appContent = fs.readFileSync(appPath, 'utf8');
  
  const routes = [
    'StorageService.isLoggedIn',
    'StorageService.removeUser',
    '/api-test'
  ];
  
  routes.forEach(route => {
    if (appContent.includes(route)) {
      log(`✅ ${route} routing configured`, 'success');
    } else {
      log(`❌ ${route} routing missing`, 'error');
    }
  });
} else {
  log('❌ App.tsx tidak ditemukan', 'error');
}

// 7. Generate test checklist
log('\n7️⃣ Generating test checklist...');

const testChecklist = `
# 📋 Test Checklist Integrasi PDAM

## Manual Testing

### 1. Login Testing
- [ ] Login dengan NIK valid
- [ ] Login dengan NIK invalid
- [ ] Auto-redirect setelah login berhasil
- [ ] Error handling untuk NIK tidak ditemukan

### 2. Dashboard Testing
- [ ] Dashboard menampilkan informasi user
- [ ] Status absensi hari ini tampil
- [ ] Data real-time dari API PDAM
- [ ] Fallback ke data offline jika API error

### 3. Absensi Testing
- [ ] Ambil foto untuk absen masuk
- [ ] GPS location terdeteksi
- [ ] Absen masuk berhasil dengan foto
- [ ] Status berubah ke "pulang" setelah absen masuk
- [ ] Absen pulang berhasil dengan foto
- [ ] Data tersimpan di server PDAM

### 4. Histori Testing
- [ ] Load histori dari API PDAM
- [ ] Filter berdasarkan bulan/tahun
- [ ] Filter berdasarkan status
- [ ] Foto absensi bisa dilihat
- [ ] Data sorting berdasarkan tanggal

### 5. API Testing
- [ ] Buka halaman /api-test
- [ ] Health check server berhasil
- [ ] Semua endpoint test PASS
- [ ] Error handling untuk API failure

### 6. Offline Testing
- [ ] Matikan koneksi internet
- [ ] Absensi offline tersimpan
- [ ] Data sync otomatis saat online
- [ ] Fallback ke data offline di dashboard

### 7. Photo Testing
- [ ] Foto wajib untuk absensi
- [ ] Foto ter-upload ke server
- [ ] Foto tampil di histori
- [ ] Foto bisa di-zoom/download

### 8. GPS Testing
- [ ] GPS permission granted
- [ ] Location accuracy dalam radius
- [ ] Error handling untuk GPS disabled
- [ ] Fallback untuk GPS tidak tersedia

## API Endpoint Testing

### Authentication
- [ ] POST /auth.php?action=login
- [ ] POST /auth.php?action=verify
- [ ] GET /auth.php?action=profile

### Attendance
- [ ] POST /attendance.php?action=checkin
- [ ] POST /attendance.php?action=checkout
- [ ] GET /attendance.php?action=today
- [ ] GET /attendance.php?action=history
- [ ] GET /attendance.php?action=locations

### Schedule
- [ ] GET /schedule.php?action=current
- [ ] GET /schedule.php?action=monthly

## Performance Testing

### Load Time
- [ ] Login < 3 detik
- [ ] Dashboard load < 5 detik
- [ ] Histori load < 10 detik
- [ ] Photo upload < 15 detik

### Memory Usage
- [ ] App tidak memory leak
- [ ] Photo tidak menyebabkan crash
- [ ] Offline data tidak unlimited growth

### Battery Usage
- [ ] GPS tidak drain battery berlebihan
- [ ] Camera usage optimal
- [ ] Background sync minimal

## Security Testing

### Data Protection
- [ ] NIK tidak tersimpan plain text
- [ ] Photo di-encode base64
- [ ] API calls menggunakan HTTPS
- [ ] Session management secure

### Permission
- [ ] Camera permission handled
- [ ] Location permission handled
- [ ] Storage permission handled
- [ ] Network permission handled

## Compatibility Testing

### Device Testing
- [ ] Android 8+ compatibility
- [ ] iOS 12+ compatibility
- [ ] Various screen sizes
- [ ] Different camera resolutions

### Network Testing
- [ ] WiFi connection
- [ ] Mobile data connection
- [ ] Slow network handling
- [ ] Network switching

---

**Status**: ⏳ Pending Testing
**Last Updated**: ${new Date().toLocaleDateString('id-ID')}
`;

const checklistPath = path.join(__dirname, 'TEST_CHECKLIST.md');
fs.writeFileSync(checklistPath, testChecklist);
log('✅ Test checklist dibuat: TEST_CHECKLIST.md', 'success');

// 7. Summary
log('\n🎉 Testing Summary:', 'success');
log('✅ Konfigurasi API: Ready');
log('✅ Halaman utama: Updated');
log('✅ Testing tools: Available');
log('✅ Dokumentasi: Complete');
log('✅ Test checklist: Generated');

log('\n📋 Next Steps:');
log('1. Jalankan aplikasi: npm run dev:pdam');
log('2. Test login dengan NIK valid');
log('3. Test absensi dengan foto');
log('4. Test API endpoints di /api-test');
log('5. Ikuti checklist di TEST_CHECKLIST.md');

log('\n🔗 Quick Links:');
log('• Development: npm run dev:pdam');
log('• API Test: http://localhost:8100/api-test');
log('• Documentation: README_PDAM.md');
log('• Integration Guide: INTEGRATION_UPDATE.md');

log('\n✨ Integrasi PDAM siap untuk testing!', 'success');
