# API Dokumentasi - Sistem Absensi PDAM

## Base URL
```
http://localhost/absensipdam/api/
```

## Response Format
Semua response menggunakan format JSON dengan struktur berikut:

### Success Response
```json
{
    "success": true,
    "message": "Success message",
    "data": {}, // Optional
    "timestamp": "2025-09-08 10:30:00"
}
```

### Error Response
```json
{
    "success": false,
    "message": "Error message",
    "error_code": "ERROR_CODE", // Optional
    "timestamp": "2025-09-08 10:30:00"
}
```

## Authentication Endpoints

### 1. Login/Verify Employee
**POST** `/auth.php?action=login`

Verifikasi NIK karyawan dan mendapatkan informasi lengkap.

**Request Body:**
```json
{
    "nik": "123456789"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Login berhasil",
    "data": {
        "employee": {
            "id": 1,
            "nik": "123456789",
            "name": "<PERSON>",
            "department": "Teknik",
            "position": "Staff",
            "email": "<EMAIL>",
            "phone": "081234567890"
        },
        "schedule": {
            "id": 1,
            "name": "Shift Pagi",
            "start_time": "07:00:00",
            "end_time": "15:00:00",
            "is_cross_day": false,
            "early_check_in_limit": "07:00:00",
            "late_check_in_limit": "08:00:00"
        },
        "today_attendance": {
            "check_in_time": "2025-09-08 07:30:00",
            "check_out_time": null,
            "status": "present"
        }
    }
}
```

### 2. Verify NIK Only
**POST** `/auth.php?action=verify`

Hanya verifikasi NIK tanpa informasi detail.

**Request Body:**
```json
{
    "nik": "123456789"
}
```

### 3. Get Employee Profile
**GET** `/auth.php?action=profile&nik=123456789`

Mendapatkan profil lengkap karyawan termasuk riwayat jadwal dan absensi.

## Attendance Endpoints

### 1. Check In
**POST** `/attendance.php?action=checkin`

Melakukan absen masuk.

**Request Body:**
```json
{
    "nik": "123456789",
    "latitude": -7.2575,
    "longitude": 112.7521,
    "location_id": 1, // Optional, jika tidak ada akan auto-detect
    "photo": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..." // Optional, foto selfie untuk validasi
}
```

**Note:**
- `photo` field is optional but recommended for validation
- Photo should be base64 encoded image (JPEG/PNG)
- Maximum file size: 5MB

**Response:**
```json
{
    "success": true,
    "message": "Absen masuk berhasil",
    "data": {
        "attendance_id": 123,
        "check_in_time": "2025-09-08 07:30:00",
        "status": "present",
        "location": {
            "id": 1,
            "name": "Kantor Pusat PDAM",
            "distance": 15.5
        }
    }
}
```

### 2. Check Out
**POST** `/attendance.php?action=checkout`

Melakukan absen keluar.

**Request Body:**
```json
{
    "nik": "123456789",
    "latitude": -7.2575,
    "longitude": 112.7521,
    "photo": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..." // Optional, foto selfie untuk validasi absen pulang
}
```

**Note:**
- `photo` field is optional but recommended for validation
- Photo should be base64 encoded image (JPEG/PNG)
- Maximum file size: 5MB

### 3. Get Attendance History
**GET** `/attendance.php?action=history&nik=123456789&start_date=2025-09-01&end_date=2025-09-30&limit=30`

Mendapatkan riwayat absensi.

**Parameters:**
- `nik` (required): NIK karyawan
- `start_date` (optional): Tanggal mulai (default: awal bulan)
- `end_date` (optional): Tanggal akhir (default: hari ini)
- `limit` (optional): Jumlah record (default: 30)

### 4. Get Today's Attendance
**GET** `/attendance.php?action=today&nik=123456789`

Mendapatkan data absensi hari ini.

### 5. Get Attendance Locations
**GET** `/attendance.php?action=locations`

Mendapatkan daftar lokasi absensi yang aktif.

## Schedule Endpoints

### 1. Get Current Schedule
**GET** `/schedule.php?action=current&nik=123456789&date=2025-09-08`

Mendapatkan jadwal kerja untuk tanggal tertentu.

**Parameters:**
- `nik` (required): NIK karyawan
- `date` (optional): Tanggal (default: hari ini)

### 2. Get Monthly Schedule
**GET** `/schedule.php?action=monthly&nik=123456789&month=2025-09`

Mendapatkan jadwal kerja bulanan.

**Parameters:**
- `nik` (required): NIK karyawan
- `month` (optional): Bulan dalam format YYYY-MM (default: bulan ini)

### 3. Get Schedule Swaps
**GET** `/schedule.php?action=swaps&nik=123456789&status=pending`

Mendapatkan daftar pertukaran jadwal.

**Parameters:**
- `nik` (required): NIK karyawan
- `status` (optional): Status (pending, approved, rejected)

### 4. Request Schedule Swap
**POST** `/schedule.php?action=request_swap`

Membuat permintaan pertukaran jadwal.

**Request Body:**
```json
{
    "nik": "123456789",
    "target_nik": "987654321",
    "swap_date": "2025-09-15",
    "notes": "Alasan pertukaran jadwal"
}
```

## Error Codes

| Code | Description |
|------|-------------|
| `MISSING_FIELDS` | Field yang diperlukan tidak ada |
| `INVALID_NIK` | NIK tidak ditemukan atau tidak aktif |
| `INVALID_LOCATION` | Lokasi absensi tidak valid |
| `RATE_LIMIT_EXCEEDED` | Batas request terlampaui |
| `DATABASE_ERROR` | Error database |

## Rate Limiting

API memiliki rate limiting 1000 request per jam per IP address.

## Security

- Semua input di-sanitize untuk mencegah injection
- CORS headers sudah dikonfigurasi
- Activity logging untuk semua request
- Validasi lokasi untuk absensi

## Testing

Anda dapat menggunakan tools seperti Postman atau curl untuk testing API:

```bash
# Test login
curl -X POST http://localhost/absensipdam/api/auth.php?action=login \
  -H "Content-Type: application/json" \
  -d '{"nik":"123456789"}'

# Test check-in
curl -X POST http://localhost/absensipdam/api/attendance.php?action=checkin \
  -H "Content-Type: application/json" \
  -d '{"nik":"123456789","latitude":-7.2575,"longitude":112.7521}'
```

## Notes

- Semua waktu menggunakan timezone Asia/Jakarta
- Untuk shift lintas hari, tanggal absensi mengikuti tanggal mulai shift
- Validasi lokasi menggunakan radius yang sudah ditentukan
- API mendukung pertukaran jadwal dengan approval workflow
