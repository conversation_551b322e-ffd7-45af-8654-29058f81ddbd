<?php
/**
 * Attendance API Endpoints
 * Endpoint untuk absensi masuk dan keluar
 */

require_once 'config.php';

// Helper function to handle photo upload
function handlePhotoUpload($photoData, $employeeId, $type) {
    if (empty($photoData)) {
        return null;
    }

    // Create upload directory if not exists
    $uploadDir = '../uploads/attendance_photos/';
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }

    // Generate unique filename
    $timestamp = date('YmdHis');
    $filename = "{$timestamp}_{$type}_emp{$employeeId}.jpg";
    $filepath = $uploadDir . $filename;

    // Decode base64 image
    if (strpos($photoData, 'data:image/') === 0) {
        // Remove data:image/jpeg;base64, part
        $photoData = preg_replace('/^data:image\/[^;]+;base64,/', '', $photoData);
    }

    $imageData = base64_decode($photoData);

    if ($imageData === false) {
        throw new Exception('Invalid image data');
    }

    // Validate image size (max 5MB)
    if (strlen($imageData) > 5 * 1024 * 1024) {
        throw new Exception('Image size too large (max 5MB)');
    }

    // Save image
    if (file_put_contents($filepath, $imageData) === false) {
        throw new Exception('Failed to save image');
    }

    return $filename;
}

$method = $_SERVER['REQUEST_METHOD'];
$path = $_GET['action'] ?? '';

switch ($method) {
    case 'POST':
        if ($path === 'checkin') {
            handleCheckIn();
        } elseif ($path === 'checkout') {
            handleCheckOut();
        } else {
            sendError('Invalid endpoint', 404);
        }
        break;
        
    case 'GET':
        if ($path === 'history') {
            handleGetHistory();
        } elseif ($path === 'today') {
            handleGetToday();
        } elseif ($path === 'locations') {
            handleGetLocations();
        } else {
            sendError('Invalid endpoint', 404);
        }
        break;
        
    default:
        sendError('Method not allowed', 405);
}

/**
 * Handle check-in
 */
function handleCheckIn() {
    $input = getJsonInput();
    validateRequired($input, ['nik', 'latitude', 'longitude']);
    
    $nik = sanitizeInput($input['nik']);
    $latitude = (float)$input['latitude'];
    $longitude = (float)$input['longitude'];
    $locationId = isset($input['location_id']) ? (int)$input['location_id'] : null;
    $photo = isset($input['photo']) ? $input['photo'] : null;
    
    try {
        // Authenticate employee
        $employee = authenticateEmployee($nik);
        
        // Validate location
        $locationValidation = validateAttendanceLocation($GLOBALS['db'], $latitude, $longitude, $locationId);
        
        if (!$locationValidation['valid']) {
            sendError($locationValidation['message'], 400, 'INVALID_LOCATION');
        }
        
        // Handle photo upload
        $photoFilename = null;
        if ($photo) {
            try {
                $photoFilename = handlePhotoUpload($photo, $employee['id'], 'checkin');
            } catch (Exception $e) {
                sendError('Photo upload failed: ' . $e->getMessage(), 400);
            }
        }

        // Process check-in
        $result = processCheckIn(
            $GLOBALS['db'],
            $employee['id'],
            $latitude,
            $longitude,
            $locationValidation['location']['id'],
            $photoFilename
        );
        
        if ($result['success']) {
            $responseData = [
                'attendance_id' => $result['attendance_id'],
                'check_in_time' => $result['time'],
                'status' => $result['status'],
                'location' => [
                    'id' => $locationValidation['location']['id'],
                    'name' => $locationValidation['location']['name'],
                    'distance' => round($locationValidation['distance'], 2)
                ]
            ];
            
            logApiActivity('/api/attendance.php?action=checkin', 'POST', $nik, 'Check-in successful');
            sendSuccess($responseData, $result['message']);
        } else {
            logApiActivity('/api/attendance.php?action=checkin', 'POST', $nik, 'Check-in failed: ' . $result['message']);
            sendError($result['message'], 400);
        }
        
    } catch (Exception $e) {
        logApiActivity('/api/attendance.php?action=checkin', 'POST', $nik, 'Check-in error: ' . $e->getMessage());
        sendError('Gagal melakukan absen masuk: ' . $e->getMessage(), 500);
    }
}

/**
 * Handle check-out
 */
function handleCheckOut() {
    $input = getJsonInput();
    validateRequired($input, ['nik', 'latitude', 'longitude']);
    
    $nik = sanitizeInput($input['nik']);
    $latitude = (float)$input['latitude'];
    $longitude = (float)$input['longitude'];
    $photo = isset($input['photo']) ? $input['photo'] : null;
    
    try {
        // Authenticate employee
        $employee = authenticateEmployee($nik);
        
        // Handle photo upload
        $photoFilename = null;
        if ($photo) {
            try {
                $photoFilename = handlePhotoUpload($photo, $employee['id'], 'checkout');
            } catch (Exception $e) {
                sendError('Photo upload failed: ' . $e->getMessage(), 400);
            }
        }

        // Process check-out
        $result = processCheckOut($GLOBALS['db'], $employee['id'], $latitude, $longitude, $photoFilename);
        
        if ($result['success']) {
            $responseData = [
                'attendance_id' => $result['attendance_id'],
                'check_out_time' => $result['time']
            ];
            
            logApiActivity('/api/attendance.php?action=checkout', 'POST', $nik, 'Check-out successful');
            sendSuccess($responseData, $result['message']);
        } else {
            logApiActivity('/api/attendance.php?action=checkout', 'POST', $nik, 'Check-out failed: ' . $result['message']);
            sendError($result['message'], 400);
        }
        
    } catch (Exception $e) {
        logApiActivity('/api/attendance.php?action=checkout', 'POST', $nik, 'Check-out error: ' . $e->getMessage());
        sendError('Gagal melakukan absen keluar: ' . $e->getMessage(), 500);
    }
}

/**
 * Handle get attendance history
 */
function handleGetHistory() {
    $nik = $_GET['nik'] ?? '';
    $startDate = $_GET['start_date'] ?? date('Y-m-01'); // Default to start of current month
    $endDate = $_GET['end_date'] ?? date('Y-m-d'); // Default to today
    $limit = (int)($_GET['limit'] ?? 30); // Default 30 records
    
    if (empty($nik)) {
        sendError('NIK parameter required', 400);
    }
    
    try {
        $employee = authenticateEmployee($nik);
        
        $attendances = $GLOBALS['db']->fetchAll(
            "SELECT a.*, l.name as location_name, ws.name as schedule_name
             FROM attendance a
             LEFT JOIN locations l ON a.location_id = l.id
             LEFT JOIN work_schedules ws ON a.work_schedule_id = ws.id
             WHERE a.employee_id = ? 
             AND a.attendance_date BETWEEN ? AND ?
             ORDER BY a.attendance_date DESC
             LIMIT ?",
            [$employee['id'], $startDate, $endDate, $limit]
        );
        
        $responseData = [
            'attendances' => $attendances,
            'period' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ],
            'summary' => [
                'total_days' => count($attendances),
                'present_days' => count(array_filter($attendances, function($a) { return $a['status'] == 'present'; })),
                'late_days' => count(array_filter($attendances, function($a) { return $a['status'] == 'late'; })),
                'absent_days' => count(array_filter($attendances, function($a) { return $a['status'] == 'absent'; }))
            ]
        ];
        
        logApiActivity('/api/attendance.php?action=history', 'GET', $nik, 'History retrieved');
        sendSuccess($responseData, 'Riwayat absensi berhasil diambil');
        
    } catch (Exception $e) {
        sendError('Gagal mengambil riwayat absensi: ' . $e->getMessage(), 500);
    }
}

/**
 * Handle get today's attendance
 */
function handleGetToday() {
    $nik = $_GET['nik'] ?? '';
    
    if (empty($nik)) {
        sendError('NIK parameter required', 400);
    }
    
    try {
        $employee = authenticateEmployee($nik);
        
        // Get today's attendance
        $todayAttendance = $GLOBALS['db']->fetchOne(
            "SELECT a.*, l.name as location_name, ws.name as schedule_name,
                    ws.start_time, ws.end_time, ws.is_cross_day
             FROM attendance a
             LEFT JOIN locations l ON a.location_id = l.id
             LEFT JOIN work_schedules ws ON a.work_schedule_id = ws.id
             WHERE a.employee_id = ? AND a.attendance_date = ?",
            [$employee['id'], date('Y-m-d')]
        );
        
        // Get current schedule
        $currentSchedule = getEmployeeSchedule($GLOBALS['db'], $employee['id'], date('Y-m-d'));
        
        // Check for schedule swap today
        $swappedSchedule = getScheduleSwap($GLOBALS['db'], $employee['id'], date('Y-m-d'));
        if ($swappedSchedule) {
            $currentSchedule = $swappedSchedule;
        }
        
        $responseData = [
            'attendance' => $todayAttendance,
            'schedule' => $currentSchedule,
            'can_check_in' => !$todayAttendance || !$todayAttendance['check_in_time'],
            'can_check_out' => $todayAttendance && $todayAttendance['check_in_time'] && !$todayAttendance['check_out_time']
        ];
        
        logApiActivity('/api/attendance.php?action=today', 'GET', $nik, 'Today attendance retrieved');
        sendSuccess($responseData, 'Data absensi hari ini berhasil diambil');
        
    } catch (Exception $e) {
        sendError('Gagal mengambil data absensi hari ini: ' . $e->getMessage(), 500);
    }
}

/**
 * Handle get attendance locations
 */
function handleGetLocations() {
    try {
        $locations = $GLOBALS['db']->fetchAll(
            "SELECT id, name, latitude, longitude, radius, address
             FROM locations 
             WHERE is_active = 1
             ORDER BY name"
        );
        
        logApiActivity('/api/attendance.php?action=locations', 'GET', null, 'Locations retrieved');
        sendSuccess($locations, 'Daftar lokasi absensi berhasil diambil');
        
    } catch (Exception $e) {
        sendError('Gagal mengambil daftar lokasi: ' . $e->getMessage(), 500);
    }
}
?>
