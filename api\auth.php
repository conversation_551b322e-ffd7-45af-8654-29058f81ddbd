<?php
/**
 * Authentication API Endpoints
 * Endpoint untuk autentikasi dan informasi karyawan
 */

require_once 'config.php';

$method = $_SERVER['REQUEST_METHOD'];
$path = $_GET['action'] ?? '';

switch ($method) {
    case 'POST':
        if ($path === 'login') {
            handleLogin();
        } elseif ($path === 'verify') {
            handleVerifyNik();
        } else {
            sendError('Invalid endpoint', 404);
        }
        break;
        
    case 'GET':
        if ($path === 'profile') {
            handleGetProfile();
        } else {
            sendError('Invalid endpoint', 404);
        }
        break;
        
    default:
        sendError('Method not allowed', 405);
}

/**
 * Handle employee login/verification
 */
function handleLogin() {
    $input = getJsonInput();
    validateRequired($input, ['nik']);
    
    $nik = sanitizeInput($input['nik']);
    
    try {
        $employee = authenticateEmployee($nik);
        
        // Get current schedule
        $currentSchedule = getEmployeeSchedule($GLOBALS['db'], $employee['id'], date('Y-m-d'));
        
        // Check for schedule swap today
        $swappedSchedule = getScheduleSwap($GLOBALS['db'], $employee['id'], date('Y-m-d'));
        if ($swappedSchedule) {
            $currentSchedule = $swappedSchedule;
        }
        
        // Get today's attendance
        $todayAttendance = $GLOBALS['db']->fetchOne(
            "SELECT * FROM attendance WHERE employee_id = ? AND attendance_date = ?",
            [$employee['id'], date('Y-m-d')]
        );
        
        $responseData = [
            'employee' => [
                'id' => $employee['id'],
                'nik' => $employee['nik'],
                'name' => $employee['name'],
                'department' => $employee['department_name'],
                'position' => $employee['position_name'],
                'email' => $employee['email'],
                'phone' => $employee['phone']
            ],
            'schedule' => $currentSchedule ? [
                'id' => $currentSchedule['id'],
                'name' => $currentSchedule['name'],
                'start_time' => $currentSchedule['start_time'],
                'end_time' => $currentSchedule['end_time'],
                'is_cross_day' => (bool)$currentSchedule['is_cross_day'],
                'early_check_in_limit' => $currentSchedule['early_check_in_limit'],
                'late_check_in_limit' => $currentSchedule['late_check_in_limit']
            ] : null,
            'today_attendance' => $todayAttendance ? [
                'check_in_time' => $todayAttendance['check_in_time'],
                'check_out_time' => $todayAttendance['check_out_time'],
                'status' => $todayAttendance['status']
            ] : null
        ];
        
        logApiActivity('/api/auth.php?action=login', 'POST', $nik, 'Employee login successful');
        sendSuccess($responseData, 'Login berhasil');
        
    } catch (Exception $e) {
        logApiActivity('/api/auth.php?action=login', 'POST', $nik, 'Login failed: ' . $e->getMessage());
        sendError('Login gagal: ' . $e->getMessage(), 401);
    }
}

/**
 * Handle NIK verification only
 */
function handleVerifyNik() {
    $input = getJsonInput();
    validateRequired($input, ['nik']);
    
    $nik = sanitizeInput($input['nik']);
    
    try {
        $employee = authenticateEmployee($nik);
        
        $responseData = [
            'nik' => $employee['nik'],
            'name' => $employee['name'],
            'department' => $employee['department_name'],
            'position' => $employee['position_name']
        ];
        
        logApiActivity('/api/auth.php?action=verify', 'POST', $nik, 'NIK verification successful');
        sendSuccess($responseData, 'NIK valid');
        
    } catch (Exception $e) {
        logApiActivity('/api/auth.php?action=verify', 'POST', $nik, 'NIK verification failed');
        sendError('NIK tidak valid', 401);
    }
}

/**
 * Handle get employee profile
 */
function handleGetProfile() {
    $nik = $_GET['nik'] ?? '';
    
    if (empty($nik)) {
        sendError('NIK parameter required', 400);
    }
    
    try {
        $employee = authenticateEmployee($nik);
        
        // Get employee schedules
        $schedules = $GLOBALS['db']->fetchAll(
            "SELECT ws.*, es.start_date, es.end_date, es.is_active as assignment_active
             FROM work_schedules ws
             JOIN employee_schedules es ON ws.id = es.work_schedule_id
             WHERE es.employee_id = ?
             ORDER BY es.start_date DESC",
            [$employee['id']]
        );
        
        // Get recent attendance (last 7 days)
        $recentAttendance = $GLOBALS['db']->fetchAll(
            "SELECT attendance_date, check_in_time, check_out_time, status
             FROM attendance 
             WHERE employee_id = ? 
             AND attendance_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
             ORDER BY attendance_date DESC",
            [$employee['id']]
        );
        
        $responseData = [
            'employee' => [
                'id' => $employee['id'],
                'nik' => $employee['nik'],
                'name' => $employee['name'],
                'department' => $employee['department_name'],
                'position' => $employee['position_name'],
                'email' => $employee['email'],
                'phone' => $employee['phone'],
                'hire_date' => $employee['hire_date'],
                'address' => $employee['address']
            ],
            'schedules' => $schedules,
            'recent_attendance' => $recentAttendance
        ];
        
        logApiActivity('/api/auth.php?action=profile', 'GET', $nik, 'Profile retrieved');
        sendSuccess($responseData, 'Data profil berhasil diambil');
        
    } catch (Exception $e) {
        sendError('Gagal mengambil data profil: ' . $e->getMessage(), 500);
    }
}
?>
