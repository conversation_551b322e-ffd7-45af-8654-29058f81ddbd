<?php
/**
 * API Configuration
 * Konfigurasi untuk API aplikasi absensi PDAM
 */

// Include main config
require_once '../config/config.php';
require_once '../includes/attendance_helper.php';

// API Settings
define('API_VERSION', '1.0');
define('API_KEY_REQUIRED', false); // Set to true if you want to require API key

// CORS Headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');
header('Content-Type: application/json; charset=utf-8');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

/**
 * Send JSON response
 */
function sendResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit();
}

/**
 * Send error response
 */
function sendError($message, $statusCode = 400, $errorCode = null) {
    $response = [
        'success' => false,
        'message' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    if ($errorCode) {
        $response['error_code'] = $errorCode;
    }
    
    sendResponse($response, $statusCode);
}

/**
 * Send success response
 */
function sendSuccess($data = null, $message = 'Success') {
    $response = [
        'success' => true,
        'message' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    sendResponse($response);
}

/**
 * Get JSON input
 */
function getJsonInput() {
    $input = file_get_contents('php://input');
    return json_decode($input, true);
}

/**
 * Validate required fields
 */
function validateRequired($data, $requiredFields) {
    $missing = [];
    
    foreach ($requiredFields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            $missing[] = $field;
        }
    }
    
    if (!empty($missing)) {
        sendError('Missing required fields: ' . implode(', ', $missing), 400, 'MISSING_FIELDS');
    }
}

/**
 * Authenticate employee by NIK and password (if implemented)
 * For now, we'll just validate NIK exists
 */
function authenticateEmployee($nik) {
    global $db;
    
    try {
        $employee = $db->fetchOne(
            "SELECT e.*, d.name as department_name, p.name as position_name 
             FROM employees e 
             LEFT JOIN departments d ON e.department_id = d.id 
             LEFT JOIN positions p ON e.position_id = p.id 
             WHERE e.nik = ? AND e.is_active = 1",
            [$nik]
        );
        
        if (!$employee) {
            sendError('NIK tidak ditemukan atau tidak aktif', 401, 'INVALID_NIK');
        }
        
        return $employee;
        
    } catch (Exception $e) {
        sendError('Database error: ' . $e->getMessage(), 500, 'DATABASE_ERROR');
    }
}

/**
 * Log API activity
 */
function logApiActivity($endpoint, $method, $nik = null, $description = '') {
    global $db;
    
    try {
        // Create API log table if not exists
        $sql = "CREATE TABLE IF NOT EXISTS api_logs (
            id INT PRIMARY KEY AUTO_INCREMENT,
            endpoint VARCHAR(100),
            method VARCHAR(10),
            nik VARCHAR(20),
            description TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $db->query($sql);
        
        $data = [
            'endpoint' => $endpoint,
            'method' => $method,
            'nik' => $nik,
            'description' => $description,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];
        
        $db->insert('api_logs', $data);
        
    } catch (Exception $e) {
        // Log error but don't stop execution
        error_log('API Log Error: ' . $e->getMessage());
    }
}

/**
 * Rate limiting (simple implementation)
 */
function checkRateLimit($identifier, $maxRequests = 100, $timeWindow = 3600) {
    global $db;
    
    try {
        // Create rate limit table if not exists
        $sql = "CREATE TABLE IF NOT EXISTS rate_limits (
            id INT PRIMARY KEY AUTO_INCREMENT,
            identifier VARCHAR(100),
            requests INT DEFAULT 1,
            window_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_identifier (identifier)
        )";
        $db->query($sql);
        
        // Clean old records
        $db->query("DELETE FROM rate_limits WHERE window_start < DATE_SUB(NOW(), INTERVAL ? SECOND)", [$timeWindow]);
        
        // Check current requests
        $current = $db->fetchOne(
            "SELECT requests FROM rate_limits WHERE identifier = ? AND window_start > DATE_SUB(NOW(), INTERVAL ? SECOND)",
            [$identifier, $timeWindow]
        );
        
        if ($current) {
            if ($current['requests'] >= $maxRequests) {
                sendError('Rate limit exceeded. Try again later.', 429, 'RATE_LIMIT_EXCEEDED');
            }
            
            // Update request count
            $db->query(
                "UPDATE rate_limits SET requests = requests + 1 WHERE identifier = ?",
                [$identifier]
            );
        } else {
            // Insert new record
            $db->insert('rate_limits', ['identifier' => $identifier]);
        }
        
    } catch (Exception $e) {
        // If rate limiting fails, continue without blocking
        error_log('Rate Limit Error: ' . $e->getMessage());
    }
}

// Apply rate limiting based on IP address
$clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
checkRateLimit($clientIp, 1000, 3600); // 1000 requests per hour per IP

// Log API request
$endpoint = $_SERVER['REQUEST_URI'] ?? '';
$method = $_SERVER['REQUEST_METHOD'] ?? '';
logApiActivity($endpoint, $method);
?>
