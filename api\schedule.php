<?php
/**
 * Schedule API Endpoints
 * Endpoint untuk jadwal kerja dan pertukaran jadwal
 */

require_once 'config.php';

$method = $_SERVER['REQUEST_METHOD'];
$path = $_GET['action'] ?? '';

switch ($method) {
    case 'GET':
        if ($path === 'current') {
            handleGetCurrentSchedule();
        } elseif ($path === 'monthly') {
            handleGetMonthlySchedule();
        } elseif ($path === 'swaps') {
            handleGetScheduleSwaps();
        } else {
            sendError('Invalid endpoint', 404);
        }
        break;
        
    case 'POST':
        if ($path === 'request_swap') {
            handleRequestSwap();
        } else {
            sendError('Invalid endpoint', 404);
        }
        break;
        
    default:
        sendError('Method not allowed', 405);
}

/**
 * Handle get current schedule
 */
function handleGetCurrentSchedule() {
    $nik = $_GET['nik'] ?? '';
    $date = $_GET['date'] ?? date('Y-m-d');
    
    if (empty($nik)) {
        sendError('NIK parameter required', 400);
    }
    
    try {
        $employee = authenticateEmployee($nik);
        
        // Get current schedule
        $schedule = getEmployeeSchedule($GLOBALS['db'], $employee['id'], $date);
        
        // Check for schedule swap
        $swappedSchedule = getScheduleSwap($GLOBALS['db'], $employee['id'], $date);
        $isSwapped = false;
        
        if ($swappedSchedule) {
            $schedule = $swappedSchedule;
            $isSwapped = true;
        }
        
        $responseData = [
            'date' => $date,
            'schedule' => $schedule,
            'is_swapped' => $isSwapped
        ];
        
        logApiActivity('/api/schedule.php?action=current', 'GET', $nik, 'Current schedule retrieved');
        sendSuccess($responseData, 'Jadwal berhasil diambil');
        
    } catch (Exception $e) {
        sendError('Gagal mengambil jadwal: ' . $e->getMessage(), 500);
    }
}

/**
 * Handle get monthly schedule
 */
function handleGetMonthlySchedule() {
    $nik = $_GET['nik'] ?? '';
    $month = $_GET['month'] ?? date('Y-m');
    
    if (empty($nik)) {
        sendError('NIK parameter required', 400);
    }
    
    try {
        $employee = authenticateEmployee($nik);
        
        $startDate = $month . '-01';
        $endDate = date('Y-m-t', strtotime($startDate));
        
        // Get schedule assignments for the month
        $scheduleAssignments = $GLOBALS['db']->fetchAll(
            "SELECT es.*, ws.name, ws.start_time, ws.end_time, ws.is_cross_day
             FROM employee_schedules es
             JOIN work_schedules ws ON es.work_schedule_id = ws.id
             WHERE es.employee_id = ? 
             AND es.is_active = 1
             AND es.start_date <= ?
             AND (es.end_date IS NULL OR es.end_date >= ?)
             ORDER BY es.start_date",
            [$employee['id'], $endDate, $startDate]
        );
        
        // Get schedule swaps for the month
        $swaps = $GLOBALS['db']->fetchAll(
            "SELECT ss.*, ws.name, ws.start_time, ws.end_time, ws.is_cross_day,
                    CASE 
                        WHEN ss.employee_a_id = ? THEN ss.original_schedule_b_id
                        ELSE ss.original_schedule_a_id
                    END as swapped_schedule_id
             FROM schedule_swaps ss
             JOIN work_schedules ws ON (
                 CASE 
                     WHEN ss.employee_a_id = ? THEN ss.original_schedule_b_id
                     ELSE ss.original_schedule_a_id
                 END
             ) = ws.id
             WHERE (ss.employee_a_id = ? OR ss.employee_b_id = ?)
             AND ss.swap_date BETWEEN ? AND ?
             AND ss.status = 'approved'",
            [$employee['id'], $employee['id'], $employee['id'], $employee['id'], $startDate, $endDate]
        );
        
        // Get attendance for the month
        $attendances = $GLOBALS['db']->fetchAll(
            "SELECT attendance_date, check_in_time, check_out_time, status
             FROM attendance
             WHERE employee_id = ?
             AND attendance_date BETWEEN ? AND ?
             ORDER BY attendance_date",
            [$employee['id'], $startDate, $endDate]
        );
        
        $responseData = [
            'month' => $month,
            'schedule_assignments' => $scheduleAssignments,
            'schedule_swaps' => $swaps,
            'attendances' => $attendances
        ];
        
        logApiActivity('/api/schedule.php?action=monthly', 'GET', $nik, 'Monthly schedule retrieved');
        sendSuccess($responseData, 'Jadwal bulanan berhasil diambil');
        
    } catch (Exception $e) {
        sendError('Gagal mengambil jadwal bulanan: ' . $e->getMessage(), 500);
    }
}

/**
 * Handle get schedule swaps
 */
function handleGetScheduleSwaps() {
    $nik = $_GET['nik'] ?? '';
    $status = $_GET['status'] ?? ''; // pending, approved, rejected
    
    if (empty($nik)) {
        sendError('NIK parameter required', 400);
    }
    
    try {
        $employee = authenticateEmployee($nik);
        
        $whereClause = "(ss.employee_a_id = ? OR ss.employee_b_id = ?)";
        $params = [$employee['id'], $employee['id']];
        
        if (!empty($status)) {
            $whereClause .= " AND ss.status = ?";
            $params[] = $status;
        }
        
        $swaps = $GLOBALS['db']->fetchAll(
            "SELECT ss.*, 
                    ea.name as employee_a_name, ea.nik as employee_a_nik,
                    eb.name as employee_b_name, eb.nik as employee_b_nik,
                    wsa.name as schedule_a_name, wsa.start_time as schedule_a_start, wsa.end_time as schedule_a_end,
                    wsb.name as schedule_b_name, wsb.start_time as schedule_b_start, wsb.end_time as schedule_b_end
             FROM schedule_swaps ss
             JOIN employees ea ON ss.employee_a_id = ea.id
             JOIN employees eb ON ss.employee_b_id = eb.id
             JOIN work_schedules wsa ON ss.original_schedule_a_id = wsa.id
             JOIN work_schedules wsb ON ss.original_schedule_b_id = wsb.id
             WHERE {$whereClause}
             ORDER BY ss.request_date DESC",
            $params
        );
        
        logApiActivity('/api/schedule.php?action=swaps', 'GET', $nik, 'Schedule swaps retrieved');
        sendSuccess($swaps, 'Daftar pertukaran jadwal berhasil diambil');
        
    } catch (Exception $e) {
        sendError('Gagal mengambil daftar pertukaran jadwal: ' . $e->getMessage(), 500);
    }
}

/**
 * Handle request schedule swap
 */
function handleRequestSwap() {
    $input = getJsonInput();
    validateRequired($input, ['nik', 'target_nik', 'swap_date', 'notes']);
    
    $nik = sanitizeInput($input['nik']);
    $targetNik = sanitizeInput($input['target_nik']);
    $swapDate = $input['swap_date'];
    $notes = sanitizeInput($input['notes']);
    
    try {
        // Authenticate requesting employee
        $employee = authenticateEmployee($nik);
        
        // Authenticate target employee
        $targetEmployee = authenticateEmployee($targetNik);
        
        if ($employee['id'] == $targetEmployee['id']) {
            sendError('Tidak dapat menukar jadwal dengan diri sendiri', 400);
        }
        
        // Validate swap date
        if (strtotime($swapDate) < strtotime(date('Y-m-d'))) {
            sendError('Tanggal pertukaran tidak boleh di masa lalu', 400);
        }
        
        // Get current schedules for both employees
        $scheduleA = getEmployeeSchedule($GLOBALS['db'], $employee['id'], $swapDate);
        $scheduleB = getEmployeeSchedule($GLOBALS['db'], $targetEmployee['id'], $swapDate);
        
        if (!$scheduleA || !$scheduleB) {
            sendError('Salah satu atau kedua karyawan tidak memiliki jadwal aktif untuk tanggal tersebut', 400);
        }
        
        // Check if swap already exists
        $existingSwap = $GLOBALS['db']->fetchOne(
            "SELECT id FROM schedule_swaps 
             WHERE swap_date = ? AND status != 'rejected'
             AND ((employee_a_id = ? AND employee_b_id = ?) OR (employee_a_id = ? AND employee_b_id = ?))",
            [$swapDate, $employee['id'], $targetEmployee['id'], $targetEmployee['id'], $employee['id']]
        );
        
        if ($existingSwap) {
            sendError('Permintaan pertukaran jadwal untuk tanggal ini sudah ada', 400);
        }
        
        // Create swap request
        $swapData = [
            'employee_a_id' => $employee['id'],
            'employee_b_id' => $targetEmployee['id'],
            'swap_date' => $swapDate,
            'original_schedule_a_id' => $scheduleA['id'],
            'original_schedule_b_id' => $scheduleB['id'],
            'requested_by' => 1, // Default admin ID, you might want to create a system user
            'notes' => $notes,
            'status' => 'pending'
        ];
        
        $swapId = $GLOBALS['db']->insert('schedule_swaps', $swapData);
        
        $responseData = [
            'swap_id' => $swapId,
            'status' => 'pending',
            'message' => 'Permintaan pertukaran jadwal berhasil dibuat dan menunggu persetujuan admin'
        ];
        
        logApiActivity('/api/schedule.php?action=request_swap', 'POST', $nik, "Swap request created with {$targetNik} for {$swapDate}");
        sendSuccess($responseData, 'Permintaan pertukaran jadwal berhasil dibuat');
        
    } catch (Exception $e) {
        logApiActivity('/api/schedule.php?action=request_swap', 'POST', $nik, 'Swap request failed: ' . $e->getMessage());
        sendError('Gagal membuat permintaan pertukaran jadwal: ' . $e->getMessage(), 500);
    }
}
?>
