-- Database untuk Aplikasi Absensi PDAM
-- Created: 2025-09-08

CREATE DATABASE IF NOT EXISTS absensipdam;
USE absensipdam;

-- Tabel untuk admin users
CREATE TABLE admin_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active TINYINT(1) DEFAULT 1
);

-- Tabel untuk bidang/departemen
CREATE TABLE departments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabel untuk jabatan
CREATE TABLE positions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabel untuk karyawan
CREATE TABLE employees (
    id INT PRIMARY KEY AUTO_INCREMENT,
    nik VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    department_id INT,
    position_id INT,
    phone VARCHAR(15),
    email VARCHAR(100),
    address TEXT,
    hire_date DATE,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id),
    FOREIGN KEY (position_id) REFERENCES positions(id)
);

-- Tabel untuk jam kerja template
CREATE TABLE work_schedules (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    early_check_in_limit TIME, -- Batas absen masuk paling awal
    late_check_in_limit TIME,  -- Batas absen masuk paling telat
    early_check_out_limit TIME, -- Batas absen keluar paling awal
    late_check_out_limit TIME,  -- Batas absen keluar paling telat
    is_cross_day TINYINT(1) DEFAULT 0, -- Untuk shift lintas hari
    description TEXT,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabel untuk lokasi absen
CREATE TABLE locations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    radius INT NOT NULL, -- dalam meter
    address TEXT,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel untuk jadwal kerja karyawan (assignment)
CREATE TABLE employee_schedules (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_id INT NOT NULL,
    work_schedule_id INT NOT NULL,
    location_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (employee_id) REFERENCES employees(id),
    FOREIGN KEY (work_schedule_id) REFERENCES work_schedules(id),
    FOREIGN KEY (location_id) REFERENCES locations(id)
);

-- Tabel untuk pertukaran jadwal (rolling)
CREATE TABLE schedule_swaps (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_a_id INT NOT NULL,
    employee_b_id INT NOT NULL,
    swap_date DATE NOT NULL,
    original_schedule_a_id INT NOT NULL,
    original_schedule_b_id INT NOT NULL,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    requested_by INT NOT NULL,
    approved_by INT,
    request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    approval_date TIMESTAMP NULL,
    notes TEXT,
    FOREIGN KEY (employee_a_id) REFERENCES employees(id),
    FOREIGN KEY (employee_b_id) REFERENCES employees(id),
    FOREIGN KEY (original_schedule_a_id) REFERENCES work_schedules(id),
    FOREIGN KEY (original_schedule_b_id) REFERENCES work_schedules(id),
    FOREIGN KEY (requested_by) REFERENCES admin_users(id),
    FOREIGN KEY (approved_by) REFERENCES admin_users(id)
);

-- Tabel untuk data absensi
CREATE TABLE attendance (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_id INT NOT NULL,
    attendance_date DATE NOT NULL,
    check_in_time TIMESTAMP NULL,
    check_out_time TIMESTAMP NULL,
    check_in_latitude DECIMAL(10, 8),
    check_in_longitude DECIMAL(11, 8),
    check_out_latitude DECIMAL(10, 8),
    check_out_longitude DECIMAL(11, 8),
    check_in_photo VARCHAR(255) NULL,
    check_out_photo VARCHAR(255) NULL,
    location_id INT,
    work_schedule_id INT,
    status ENUM('present', 'late', 'absent', 'half_day') DEFAULT 'present',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (employee_id) REFERENCES employees(id),
    FOREIGN KEY (location_id) REFERENCES locations(id),
    FOREIGN KEY (work_schedule_id) REFERENCES work_schedules(id),
    UNIQUE KEY unique_employee_date (employee_id, attendance_date)
);

-- Insert data default admin
INSERT INTO admin_users (username, password, full_name, email) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', '<EMAIL>');

-- Insert data default departemen
INSERT INTO departments (name, description) VALUES 
('Teknik', 'Departemen Teknik'),
('Administrasi', 'Departemen Administrasi'),
('Keuangan', 'Departemen Keuangan'),
('Operasional', 'Departemen Operasional');

-- Insert data default jabatan
INSERT INTO positions (name, description) VALUES 
('Manager', 'Manager'),
('Supervisor', 'Supervisor'),
('Staff', 'Staff'),
('Operator', 'Operator');

-- Insert data default jam kerja
INSERT INTO work_schedules (name, start_time, end_time, early_check_in_limit, late_check_in_limit, early_check_out_limit, late_check_out_limit, description) VALUES 
('Shift Pagi', '07:00:00', '15:00:00', '07:00:00', '08:00:00', '14:30:00', '15:30:00', 'Shift kerja pagi'),
('Shift Siang', '15:00:00', '22:00:00', '15:00:00', '16:00:00', '21:30:00', '22:30:00', 'Shift kerja siang'),
('Shift Malam', '22:00:00', '07:00:00', '22:00:00', '23:00:00', '06:30:00', '07:30:00', 'Shift kerja malam lintas hari');

-- Update shift malam untuk lintas hari
UPDATE work_schedules SET is_cross_day = 1 WHERE name = 'Shift Malam';

-- Insert data default lokasi
INSERT INTO locations (name, latitude, longitude, radius, address) VALUES 
('Kantor Pusat PDAM', -7.2575, 112.7521, 100, 'Jl. Raya Surabaya No. 123'),
('Instalasi Air Bersih 1', -7.2500, 112.7400, 50, 'Jl. Instalasi No. 1'),
('Instalasi Air Bersih 2', -7.2600, 112.7600, 50, 'Jl. Instalasi No. 2');
