-- Update database untuk menambahkan kolom foto pada tabel attendance
-- Jalankan script ini jika database sudah ada sebelumnya

USE absensipdam;

-- Tambahkan kolom foto check-in dan check-out
ALTER TABLE attendance 
ADD COLUMN check_in_photo VARCHAR(255) NULL AFTER check_out_longitude,
ADD COLUMN check_out_photo VARCHAR(255) NULL AFTER check_in_photo;

-- Buat folder uploads jika belum ada (dilakukan manual)
-- mkdir uploads/attendance_photos/

-- Update status enum untuk menambahkan status baru jika diperlukan
ALTER TABLE attendance 
MODIFY COLUMN status ENUM('present', 'late', 'absent', 'half_day', 'early_leave', 'overtime') DEFAULT 'present';

-- Tambahkan index untuk performance
ALTER TABLE attendance 
ADD INDEX idx_attendance_date (attendance_date),
ADD INDEX idx_employee_date (employee_id, attendance_date);

-- Buat tabel untuk log foto (opsional, untuk audit trail)
CREATE TABLE IF NOT EXISTS attendance_photo_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    attendance_id INT NOT NULL,
    photo_type ENUM('check_in', 'check_out') NOT NULL,
    original_filename VARCHAR(255),
    stored_filename VARCHAR(255),
    file_size INT,
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    uploaded_by VARCHAR(50), -- 'mobile_app' atau 'admin'
    ip_address VARCHAR(45),
    FOREIGN KEY (attendance_id) REFERENCES attendance(id) ON DELETE CASCADE
);

-- Insert sample data untuk testing (opsional)
-- INSERT INTO attendance_photo_logs (attendance_id, photo_type, original_filename, stored_filename, file_size, uploaded_by) 
-- VALUES (1, 'check_in', 'photo_checkin.jpg', '20241201_123456_checkin_emp1.jpg', 245760, 'mobile_app');

COMMIT;
