<?php
$page_title = 'Pengaturan Jadwal Karyawan';
include 'includes/header.php';

// Get employee ID from parameter
$employeeId = (int)($_GET['employee_id'] ?? 0);

if ($employeeId <= 0) {
    setAlert('danger', 'ID karyawan tidak valid');
    header('Location: employees.php');
    exit();
}

// Get employee data
try {
    $employee = $db->fetchOne(
        "SELECT e.*, d.name as department_name, p.name as position_name 
         FROM employees e 
         LEFT JOIN departments d ON e.department_id = d.id 
         LEFT JOIN positions p ON e.position_id = p.id 
         WHERE e.id = ?",
        [$employeeId]
    );
    
    if (!$employee) {
        setAlert('danger', 'Karyawan tidak ditemukan');
        header('Location: employees.php');
        exit();
    }
} catch (Exception $e) {
    setAlert('danger', 'Error: ' . $e->getMessage());
    header('Location: employees.php');
    exit();
}

// Handle form submissions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    if ($action == 'add') {
        $workScheduleId = (int)$_POST['work_schedule_id'];
        $locationId = (int)$_POST['location_id'];
        $startDate = $_POST['start_date'];
        $endDate = $_POST['end_date'] ?: null;
        
        // Validation
        $errors = [];
        if ($workScheduleId <= 0) $errors[] = 'Jadwal kerja harus dipilih';
        if ($locationId <= 0) $errors[] = 'Lokasi harus dipilih';
        if (empty($startDate)) $errors[] = 'Tanggal mulai harus diisi';
        if ($endDate && strtotime($endDate) < strtotime($startDate)) {
            $errors[] = 'Tanggal selesai tidak boleh lebih awal dari tanggal mulai';
        }
        
        if (empty($errors)) {
            try {
                // Deactivate current active schedule if exists
                $db->query(
                    "UPDATE employee_schedules SET is_active = 0 WHERE employee_id = ? AND is_active = 1",
                    [$employeeId]
                );
                
                // Insert new schedule
                $data = [
                    'employee_id' => $employeeId,
                    'work_schedule_id' => $workScheduleId,
                    'location_id' => $locationId,
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'is_active' => 1
                ];
                
                $db->insert('employee_schedules', $data);
                logActivity('ADD_EMPLOYEE_SCHEDULE', "Added schedule for employee: {$employee['name']}");
                setAlert('success', 'Jadwal karyawan berhasil ditambahkan');
                
            } catch (Exception $e) {
                setAlert('danger', 'Terjadi kesalahan: ' . $e->getMessage());
            }
        } else {
            setAlert('danger', implode('<br>', $errors));
        }
        
        header("Location: employee_schedules.php?employee_id={$employeeId}");
        exit();
    }
    
    if ($action == 'deactivate') {
        $scheduleId = (int)$_POST['schedule_id'];
        try {
            $db->update('employee_schedules', ['is_active' => 0], 'id = ? AND employee_id = ?', [$scheduleId, $employeeId]);
            logActivity('DEACTIVATE_EMPLOYEE_SCHEDULE', "Deactivated schedule for employee: {$employee['name']}");
            setAlert('success', 'Jadwal berhasil dinonaktifkan');
        } catch (Exception $e) {
            setAlert('danger', 'Terjadi kesalahan: ' . $e->getMessage());
        }
        
        header("Location: employee_schedules.php?employee_id={$employeeId}");
        exit();
    }
}

// Get employee schedules
try {
    $schedules = $db->fetchAll(
        "SELECT es.*, ws.name as schedule_name, ws.start_time, ws.end_time, ws.is_cross_day,
                l.name as location_name
         FROM employee_schedules es
         JOIN work_schedules ws ON es.work_schedule_id = ws.id
         JOIN locations l ON es.location_id = l.id
         WHERE es.employee_id = ?
         ORDER BY es.is_active DESC, es.start_date DESC",
        [$employeeId]
    );
    
    // Get available work schedules and locations
    $workSchedules = $db->fetchAll("SELECT * FROM work_schedules WHERE is_active = 1 ORDER BY name");
    $locations = $db->fetchAll("SELECT * FROM locations WHERE is_active = 1 ORDER BY name");
    
} catch (Exception $e) {
    $error = "Error loading data: " . $e->getMessage();
}
?>

<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="employees.php">Data Karyawan</a></li>
                <li class="breadcrumb-item active">Pengaturan Jadwal</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0">
                    <i class="fas fa-clock me-2"></i>Pengaturan Jadwal Karyawan
                </h1>
                <p class="text-muted mb-0">
                    <strong><?= htmlspecialchars($employee['name']) ?></strong> 
                    (NIK: <?= htmlspecialchars($employee['nik']) ?>) - 
                    <?= htmlspecialchars($employee['department_name'] ?? '') ?> / 
                    <?= htmlspecialchars($employee['position_name'] ?? '') ?>
                </p>
            </div>
            <div>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#scheduleModal">
                    <i class="fas fa-plus me-2"></i>Tambah Jadwal
                </button>
                <a href="employees.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Kembali
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Current Active Schedule -->
<?php 
$activeSchedule = array_filter($schedules, function($s) { return $s['is_active']; });
$activeSchedule = reset($activeSchedule);
?>

<?php if ($activeSchedule): ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-check-circle me-2"></i>Jadwal Aktif Saat Ini
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <strong>Jadwal Kerja:</strong><br>
                        <?= htmlspecialchars($activeSchedule['schedule_name']) ?>
                    </div>
                    <div class="col-md-3">
                        <strong>Jam Kerja:</strong><br>
                        <?= formatTime($activeSchedule['start_time']) ?> - <?= formatTime($activeSchedule['end_time']) ?>
                        <?php if ($activeSchedule['is_cross_day']): ?>
                            <span class="badge bg-warning ms-1">Lintas Hari</span>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-3">
                        <strong>Lokasi:</strong><br>
                        <?= htmlspecialchars($activeSchedule['location_name']) ?>
                    </div>
                    <div class="col-md-3">
                        <strong>Periode:</strong><br>
                        <?= formatDate($activeSchedule['start_date']) ?> - 
                        <?= $activeSchedule['end_date'] ? formatDate($activeSchedule['end_date']) : 'Sekarang' ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php else: ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Perhatian:</strong> Karyawan ini belum memiliki jadwal kerja aktif.
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Schedule History -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>Riwayat Jadwal
                </h5>
            </div>
            <div class="card-body">
                <?php if (isset($error)): ?>
                    <div class="alert alert-danger"><?= $error ?></div>
                <?php elseif (empty($schedules)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Belum ada jadwal yang ditetapkan untuk karyawan ini</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Jadwal Kerja</th>
                                    <th>Jam Kerja</th>
                                    <th>Lokasi</th>
                                    <th>Periode</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($schedules as $schedule): ?>
                                    <tr class="<?= $schedule['is_active'] ? 'table-success' : '' ?>">
                                        <td>
                                            <strong><?= htmlspecialchars($schedule['schedule_name']) ?></strong>
                                            <?php if ($schedule['is_cross_day']): ?>
                                                <br><span class="badge bg-warning">Lintas Hari</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?= formatTime($schedule['start_time']) ?> - <?= formatTime($schedule['end_time']) ?>
                                        </td>
                                        <td><?= htmlspecialchars($schedule['location_name']) ?></td>
                                        <td>
                                            <?= formatDate($schedule['start_date']) ?><br>
                                            <small class="text-muted">
                                                s/d <?= $schedule['end_date'] ? formatDate($schedule['end_date']) : 'Sekarang' ?>
                                            </small>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $schedule['is_active'] ? 'success' : 'secondary' ?>">
                                                <?= $schedule['is_active'] ? 'Aktif' : 'Tidak Aktif' ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($schedule['is_active']): ?>
                                                <button type="button" class="btn btn-sm btn-warning" 
                                                        onclick="deactivateSchedule(<?= $schedule['id'] ?>)">
                                                    <i class="fas fa-stop"></i> Nonaktifkan
                                                </button>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Schedule Modal -->
<div class="modal fade" id="scheduleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Tambah Jadwal Kerja</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" value="add">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="work_schedule_id" class="form-label">Jadwal Kerja *</label>
                            <select class="form-select" id="work_schedule_id" name="work_schedule_id" required>
                                <option value="">Pilih Jadwal Kerja</option>
                                <?php foreach ($workSchedules as $ws): ?>
                                    <option value="<?= $ws['id'] ?>" 
                                            data-start="<?= $ws['start_time'] ?>" 
                                            data-end="<?= $ws['end_time'] ?>"
                                            data-cross="<?= $ws['is_cross_day'] ?>">
                                        <?= htmlspecialchars($ws['name']) ?> 
                                        (<?= formatTime($ws['start_time']) ?> - <?= formatTime($ws['end_time']) ?>)
                                        <?= $ws['is_cross_day'] ? ' - Lintas Hari' : '' ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">Jadwal kerja harus dipilih</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="location_id" class="form-label">Lokasi Absen *</label>
                            <select class="form-select" id="location_id" name="location_id" required>
                                <option value="">Pilih Lokasi</option>
                                <?php foreach ($locations as $loc): ?>
                                    <option value="<?= $loc['id'] ?>">
                                        <?= htmlspecialchars($loc['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">Lokasi harus dipilih</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="start_date" class="form-label">Tanggal Mulai *</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" 
                                   value="<?= date('Y-m-d') ?>" required>
                            <div class="invalid-feedback">Tanggal mulai harus diisi</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="end_date" class="form-label">Tanggal Selesai</label>
                            <input type="date" class="form-control" id="end_date" name="end_date">
                            <div class="form-text">Kosongkan jika tidak ada batas waktu</div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Catatan:</strong> Menambahkan jadwal baru akan menonaktifkan jadwal yang sedang aktif.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$additional_js = "
<script>
function deactivateSchedule(scheduleId) {
    if (confirm('Apakah Anda yakin ingin menonaktifkan jadwal ini?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"deactivate\"><input type=\"hidden\" name=\"schedule_id\" value=\"' + scheduleId + '\">';
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
";
?>

<?php include 'includes/footer.php'; ?>
