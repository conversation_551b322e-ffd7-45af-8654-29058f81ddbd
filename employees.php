<?php
$page_title = 'Data Karyawan';
include 'includes/header.php';

// Handle form submissions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    if ($action == 'add' || $action == 'edit') {
        $nik = sanitizeInput($_POST['nik']);
        $name = sanitizeInput($_POST['name']);
        $department_id = (int)$_POST['department_id'];
        $position_id = (int)$_POST['position_id'];
        $phone = sanitizeInput($_POST['phone']);
        $email = sanitizeInput($_POST['email']);
        $address = sanitizeInput($_POST['address']);
        $hire_date = $_POST['hire_date'];
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        
        // Validation
        $errors = [];
        if (empty($nik)) $errors[] = 'NIK harus diisi';
        if (empty($name)) $errors[] = 'Nama harus diisi';
        if ($department_id <= 0) $errors[] = 'Departemen harus dipilih';
        if ($position_id <= 0) $errors[] = 'Jabatan harus dipilih';
        if (!empty($email) && !isValidEmail($email)) $errors[] = 'Format email tidak valid';
        
        if (empty($errors)) {
            try {
                $data = [
                    'nik' => $nik,
                    'name' => $name,
                    'department_id' => $department_id,
                    'position_id' => $position_id,
                    'phone' => $phone,
                    'email' => $email,
                    'address' => $address,
                    'hire_date' => $hire_date,
                    'is_active' => $is_active
                ];
                
                if ($action == 'add') {
                    // Check if NIK already exists
                    $existing = $db->fetchOne("SELECT id FROM employees WHERE nik = ?", [$nik]);
                    if ($existing) {
                        setAlert('danger', 'NIK sudah digunakan oleh karyawan lain');
                    } else {
                        $db->insert('employees', $data);
                        logActivity('ADD_EMPLOYEE', "Added employee: {$name} (NIK: {$nik})");
                        setAlert('success', 'Data karyawan berhasil ditambahkan');
                    }
                } else {
                    $id = (int)$_POST['id'];
                    // Check if NIK already exists for other employee
                    $existing = $db->fetchOne("SELECT id FROM employees WHERE nik = ? AND id != ?", [$nik, $id]);
                    if ($existing) {
                        setAlert('danger', 'NIK sudah digunakan oleh karyawan lain');
                    } else {
                        $db->update('employees', $data, 'id = ?', [$id]);
                        logActivity('EDIT_EMPLOYEE', "Updated employee: {$name} (NIK: {$nik})");
                        setAlert('success', 'Data karyawan berhasil diperbarui');
                    }
                }
            } catch (Exception $e) {
                setAlert('danger', 'Terjadi kesalahan: ' . $e->getMessage());
            }
        } else {
            setAlert('danger', implode('<br>', $errors));
        }
        
        header('Location: employees.php');
        exit();
    }
    
    if ($action == 'delete') {
        $id = (int)$_POST['id'];
        try {
            $employee = $db->fetchOne("SELECT name, nik FROM employees WHERE id = ?", [$id]);
            if ($employee) {
                $db->delete('employees', 'id = ?', [$id]);
                logActivity('DELETE_EMPLOYEE', "Deleted employee: {$employee['name']} (NIK: {$employee['nik']})");
                setAlert('success', 'Data karyawan berhasil dihapus');
            }
        } catch (Exception $e) {
            setAlert('danger', 'Terjadi kesalahan: ' . $e->getMessage());
        }
        
        header('Location: employees.php');
        exit();
    }
}

// Get employees data
try {
    $employees = $db->fetchAll("
        SELECT e.*, d.name as department_name, p.name as position_name,
               ws.name as current_schedule_name, ws.start_time, ws.end_time,
               l.name as current_location_name,
               es.start_date as schedule_start_date, es.end_date as schedule_end_date
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN positions p ON e.position_id = p.id
        LEFT JOIN employee_schedules es ON e.id = es.employee_id AND es.is_active = 1
            AND (es.end_date IS NULL OR es.end_date >= CURDATE())
        LEFT JOIN work_schedules ws ON es.work_schedule_id = ws.id
        LEFT JOIN locations l ON es.location_id = l.id
        ORDER BY e.created_at DESC
    ");
    
    // Get departments and positions for form
    $departments = $db->fetchAll("SELECT * FROM departments ORDER BY name");
    $positions = $db->fetchAll("SELECT * FROM positions ORDER BY name");

    // Get work schedules for form
    $workSchedules = $db->fetchAll("SELECT * FROM work_schedules WHERE is_active = 1 ORDER BY name");

    // Get locations for form
    $locations = $db->fetchAll("SELECT * FROM locations WHERE is_active = 1 ORDER BY name");
    
} catch (Exception $e) {
    $error = "Error loading data: " . $e->getMessage();
}
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-users me-2"></i>Data Karyawan
            </h1>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#employeeModal" onclick="resetForm()">
                <i class="fas fa-plus me-2"></i>Tambah Karyawan
            </button>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <?php if (isset($error)): ?>
                    <div class="alert alert-danger"><?= $error ?></div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover data-table">
                            <thead>
                                <tr>
                                    <th>NIK</th>
                                    <th>Nama</th>
                                    <th>Departemen</th>
                                    <th>Jabatan</th>
                                    <th>Jadwal Kerja</th>
                                    <th>Lokasi</th>
                                    <th>Telepon</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($employees as $employee): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($employee['nik']) ?></td>
                                        <td><?= htmlspecialchars($employee['name']) ?></td>
                                        <td><?= htmlspecialchars($employee['department_name'] ?? '-') ?></td>
                                        <td><?= htmlspecialchars($employee['position_name'] ?? '-') ?></td>
                                        <td>
                                            <?php if ($employee['current_schedule_name']): ?>
                                                <strong><?= htmlspecialchars($employee['current_schedule_name']) ?></strong><br>
                                                <small class="text-muted">
                                                    <?= formatTime($employee['start_time']) ?> - <?= formatTime($employee['end_time']) ?>
                                                </small>
                                            <?php else: ?>
                                                <span class="text-danger">Belum ada jadwal</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= htmlspecialchars($employee['current_location_name'] ?? '-') ?></td>
                                        <td><?= htmlspecialchars($employee['phone'] ?? '-') ?></td>
                                        <td>
                                            <span class="badge bg-<?= $employee['is_active'] ? 'success' : 'danger' ?>">
                                                <?= $employee['is_active'] ? 'Aktif' : 'Tidak Aktif' ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-info"
                                                        onclick="manageSchedule(<?= $employee['id'] ?>, '<?= htmlspecialchars($employee['name']) ?>')"
                                                        title="Atur Jadwal">
                                                    <i class="fas fa-clock"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-warning"
                                                        onclick="editEmployee(<?= htmlspecialchars(json_encode($employee)) ?>)"
                                                        title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger"
                                                        onclick="deleteEmployee(<?= $employee['id'] ?>, '<?= htmlspecialchars($employee['name']) ?>')"
                                                        title="Hapus">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Employee Modal -->
<div class="modal fade" id="employeeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">Tambah Karyawan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" id="formAction" value="add">
                    <input type="hidden" name="id" id="employeeId">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="nik" class="form-label">NIK *</label>
                            <input type="text" class="form-control" id="nik" name="nik" required>
                            <div class="invalid-feedback">NIK harus diisi</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Nama Lengkap *</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                            <div class="invalid-feedback">Nama harus diisi</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="department_id" class="form-label">Departemen *</label>
                            <select class="form-select" id="department_id" name="department_id" required>
                                <option value="">Pilih Departemen</option>
                                <?php foreach ($departments as $dept): ?>
                                    <option value="<?= $dept['id'] ?>"><?= htmlspecialchars($dept['name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">Departemen harus dipilih</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="position_id" class="form-label">Jabatan *</label>
                            <select class="form-select" id="position_id" name="position_id" required>
                                <option value="">Pilih Jabatan</option>
                                <?php foreach ($positions as $pos): ?>
                                    <option value="<?= $pos['id'] ?>"><?= htmlspecialchars($pos['name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">Jabatan harus dipilih</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">Telepon</label>
                            <input type="text" class="form-control" id="phone" name="phone">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="hire_date" class="form-label">Tanggal Masuk</label>
                            <input type="date" class="form-control" id="hire_date" name="hire_date">
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                <label class="form-check-label" for="is_active">
                                    Status Aktif
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">Alamat</label>
                        <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$additional_js = "
<script>
function resetForm() {
    document.getElementById('modalTitle').textContent = 'Tambah Karyawan';
    document.getElementById('formAction').value = 'add';
    document.getElementById('employeeId').value = '';
    document.querySelector('#employeeModal form').reset();
    document.querySelector('#employeeModal form').classList.remove('was-validated');
    document.getElementById('is_active').checked = true;
}

function editEmployee(employee) {
    document.getElementById('modalTitle').textContent = 'Edit Karyawan';
    document.getElementById('formAction').value = 'edit';
    document.getElementById('employeeId').value = employee.id;
    document.getElementById('nik').value = employee.nik;
    document.getElementById('name').value = employee.name;
    document.getElementById('department_id').value = employee.department_id;
    document.getElementById('position_id').value = employee.position_id;
    document.getElementById('phone').value = employee.phone || '';
    document.getElementById('email').value = employee.email || '';
    document.getElementById('hire_date').value = employee.hire_date || '';
    document.getElementById('address').value = employee.address || '';
    document.getElementById('is_active').checked = employee.is_active == 1;
    
    new bootstrap.Modal(document.getElementById('employeeModal')).show();
}

function deleteEmployee(id, name) {
    if (confirmDelete('Apakah Anda yakin ingin menghapus karyawan \"' + name + '\"?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"delete\"><input type=\"hidden\" name=\"id\" value=\"' + id + '\">';
        document.body.appendChild(form);
        form.submit();
    }
}

function manageSchedule(employeeId, employeeName) {
    window.location.href = 'employee_schedules.php?employee_id=' + employeeId;
}
</script>
";
?>

<?php include 'includes/footer.php'; ?>
