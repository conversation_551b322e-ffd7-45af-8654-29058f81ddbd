<?php
/**
 * Attendance Helper Functions
 * Fungsi-fungsi untuk menangani logika absensi termasuk shift lintas hari
 */

/**
 * Menentukan tanggal absensi untuk shift lintas hari
 * Untuk shift malam yang dimulai malam hari dan berakhir pagi hari berikutnya,
 * tanggal absensi tetap menggunakan tanggal mulai shift
 */
function getAttendanceDate($checkTime, $scheduleStartTime, $scheduleEndTime, $isCrossDay = false) {
    $checkDateTime = new DateTime($checkTime);
    $checkDate = $checkDateTime->format('Y-m-d');
    $checkTimeOnly = $checkDateTime->format('H:i:s');
    
    if (!$isCrossDay) {
        // Shift normal (tidak lintas hari)
        return $checkDate;
    }
    
    // Shift lintas hari
    // Jika waktu check-in setelah jam 12:00 (siang), maka dianggap shift hari itu
    // Jika waktu check-in sebelum jam 12:00 (pagi), maka dianggap shift hari sebelumnya
    
    if ($checkTimeOnly >= '12:00:00') {
        // Check-in di sore/malam hari - shift dimulai hari ini
        return $checkDate;
    } else {
        // Check-in di pagi hari - shift dimulai kemarin
        $yesterday = new DateTime($checkDate);
        $yesterday->modify('-1 day');
        return $yesterday->format('Y-m-d');
    }
}

/**
 * Menentukan status absensi berdasarkan waktu check-in dan jadwal
 */
function determineAttendanceStatus($checkInTime, $checkOutTime, $schedule) {
    if (!$checkInTime) {
        return 'absent';
    }
    
    $checkInDateTime = new DateTime($checkInTime);
    $checkInTimeOnly = $checkInDateTime->format('H:i:s');
    
    $lateLimit = $schedule['late_check_in_limit'];
    
    // Untuk shift lintas hari, perlu penanganan khusus
    if ($schedule['is_cross_day']) {
        // Jika check-in di pagi hari (sebelum jam 12:00), bandingkan dengan jam selesai shift
        if ($checkInTimeOnly < '12:00:00') {
            // Check-in pagi hari untuk shift malam kemarin
            $endTime = $schedule['end_time'];
            if ($checkInTimeOnly <= $endTime) {
                return 'present';
            } else {
                return 'late';
            }
        } else {
            // Check-in sore/malam hari untuk shift malam hari ini
            if ($checkInTimeOnly <= $lateLimit) {
                return 'present';
            } else {
                return 'late';
            }
        }
    } else {
        // Shift normal
        if ($checkInTimeOnly <= $lateLimit) {
            return 'present';
        } else {
            return 'late';
        }
    }
}

/**
 * Mendapatkan jadwal kerja aktif untuk karyawan pada tanggal tertentu
 */
function getEmployeeSchedule($db, $employeeId, $date) {
    $sql = "
        SELECT ws.*, es.start_date, es.end_date
        FROM work_schedules ws
        JOIN employee_schedules es ON ws.id = es.work_schedule_id
        WHERE es.employee_id = ? 
        AND es.is_active = 1
        AND es.start_date <= ?
        AND (es.end_date IS NULL OR es.end_date >= ?)
        ORDER BY es.start_date DESC
        LIMIT 1
    ";
    
    return $db->fetchOne($sql, [$employeeId, $date, $date]);
}

/**
 * Memeriksa apakah ada pertukaran jadwal untuk tanggal tertentu
 */
function getScheduleSwap($db, $employeeId, $date) {
    $sql = "
        SELECT ss.*, 
               CASE 
                   WHEN ss.employee_a_id = ? THEN ss.original_schedule_b_id
                   ELSE ss.original_schedule_a_id
               END as swapped_schedule_id
        FROM schedule_swaps ss
        WHERE (ss.employee_a_id = ? OR ss.employee_b_id = ?)
        AND ss.swap_date = ?
        AND ss.status = 'approved'
    ";
    
    $swap = $db->fetchOne($sql, [$employeeId, $employeeId, $employeeId, $date]);
    
    if ($swap) {
        // Ambil detail jadwal yang ditukar
        $schedule = $db->fetchOne("SELECT * FROM work_schedules WHERE id = ?", [$swap['swapped_schedule_id']]);
        return $schedule;
    }
    
    return null;
}

/**
 * Memproses absensi masuk
 */
function processCheckIn($db, $employeeId, $latitude, $longitude, $locationId = null, $photoFilename = null) {
    try {
        $db->beginTransaction();
        
        $currentDateTime = date('Y-m-d H:i:s');
        $currentDate = date('Y-m-d');
        
        // Cek apakah sudah absen hari ini
        $existingAttendance = $db->fetchOne(
            "SELECT * FROM attendance WHERE employee_id = ? AND attendance_date = ?",
            [$employeeId, $currentDate]
        );
        
        if ($existingAttendance && $existingAttendance['check_in_time']) {
            throw new Exception('Anda sudah melakukan absen masuk hari ini');
        }
        
        // Dapatkan jadwal kerja (cek pertukaran jadwal dulu)
        $schedule = getScheduleSwap($db, $employeeId, $currentDate);
        if (!$schedule) {
            $schedule = getEmployeeSchedule($db, $employeeId, $currentDate);
        }
        
        if (!$schedule) {
            throw new Exception('Jadwal kerja tidak ditemukan');
        }
        
        // Tentukan tanggal absensi (untuk shift lintas hari)
        $attendanceDate = getAttendanceDate($currentDateTime, $schedule['start_time'], $schedule['end_time'], $schedule['is_cross_day']);
        
        // Cek apakah sudah ada record untuk tanggal absensi yang benar
        $attendanceRecord = $db->fetchOne(
            "SELECT * FROM attendance WHERE employee_id = ? AND attendance_date = ?",
            [$employeeId, $attendanceDate]
        );
        
        // Tentukan status absensi
        $status = determineAttendanceStatus($currentDateTime, null, $schedule);
        
        $attendanceData = [
            'employee_id' => $employeeId,
            'attendance_date' => $attendanceDate,
            'check_in_time' => $currentDateTime,
            'check_in_latitude' => $latitude,
            'check_in_longitude' => $longitude,
            'location_id' => $locationId,
            'work_schedule_id' => $schedule['id'],
            'status' => $status
        ];

        // Add photo if provided
        if ($photoFilename) {
            $attendanceData['check_in_photo'] = $photoFilename;
        }
        
        if ($attendanceRecord) {
            // Update existing record
            $db->update('attendance', $attendanceData, 'id = ?', [$attendanceRecord['id']]);
            $attendanceId = $attendanceRecord['id'];
        } else {
            // Insert new record
            $attendanceId = $db->insert('attendance', $attendanceData);
        }
        
        $db->commit();
        
        return [
            'success' => true,
            'message' => 'Absen masuk berhasil',
            'attendance_id' => $attendanceId,
            'status' => $status,
            'time' => $currentDateTime
        ];
        
    } catch (Exception $e) {
        $db->rollback();
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

/**
 * Memproses absensi keluar
 */
function processCheckOut($db, $employeeId, $latitude, $longitude, $photoFilename = null) {
    try {
        $db->beginTransaction();
        
        $currentDateTime = date('Y-m-d H:i:s');
        $currentDate = date('Y-m-d');
        
        // Cari record absensi yang belum check-out
        // Untuk shift lintas hari, cek juga tanggal kemarin
        $attendanceRecord = $db->fetchOne(
            "SELECT a.*, ws.is_cross_day 
             FROM attendance a 
             JOIN work_schedules ws ON a.work_schedule_id = ws.id
             WHERE a.employee_id = ? 
             AND a.check_in_time IS NOT NULL 
             AND a.check_out_time IS NULL
             AND (a.attendance_date = ? OR (ws.is_cross_day = 1 AND a.attendance_date = DATE_SUB(?, INTERVAL 1 DAY)))
             ORDER BY a.attendance_date DESC
             LIMIT 1",
            [$employeeId, $currentDate, $currentDate]
        );
        
        if (!$attendanceRecord) {
            throw new Exception('Tidak ditemukan record absen masuk yang belum check-out');
        }
        
        // Update record dengan check-out time
        $updateData = [
            'check_out_time' => $currentDateTime,
            'check_out_latitude' => $latitude,
            'check_out_longitude' => $longitude
        ];

        // Add photo if provided
        if ($photoFilename) {
            $updateData['check_out_photo'] = $photoFilename;
        }
        
        $db->update('attendance', $updateData, 'id = ?', [$attendanceRecord['id']]);
        
        $db->commit();
        
        return [
            'success' => true,
            'message' => 'Absen keluar berhasil',
            'attendance_id' => $attendanceRecord['id'],
            'time' => $currentDateTime
        ];
        
    } catch (Exception $e) {
        $db->rollback();
        return [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

/**
 * Validasi lokasi absensi
 */
function validateAttendanceLocation($db, $latitude, $longitude, $locationId = null) {
    if (!$locationId) {
        // Jika tidak ada location_id, cari lokasi terdekat
        $locations = $db->fetchAll("SELECT * FROM locations WHERE is_active = 1");
        
        foreach ($locations as $location) {
            $distance = calculateDistance($latitude, $longitude, $location['latitude'], $location['longitude']);
            if ($distance <= $location['radius']) {
                return [
                    'valid' => true,
                    'location' => $location,
                    'distance' => $distance
                ];
            }
        }
        
        return [
            'valid' => false,
            'message' => 'Anda berada di luar area absensi yang diizinkan'
        ];
    } else {
        // Validasi dengan lokasi spesifik
        $location = $db->fetchOne("SELECT * FROM locations WHERE id = ? AND is_active = 1", [$locationId]);
        
        if (!$location) {
            return [
                'valid' => false,
                'message' => 'Lokasi absensi tidak valid'
            ];
        }
        
        $distance = calculateDistance($latitude, $longitude, $location['latitude'], $location['longitude']);
        
        if ($distance <= $location['radius']) {
            return [
                'valid' => true,
                'location' => $location,
                'distance' => $distance
            ];
        } else {
            return [
                'valid' => false,
                'message' => 'Anda berada di luar radius lokasi absensi (' . $location['name'] . ')'
            ];
        }
    }
}
?>
