<?php
$page_title = 'Dashboard';
include 'includes/header.php';

// Get statistics
try {
    // Total employees
    $totalEmployees = $db->fetchOne("SELECT COUNT(*) as total FROM employees WHERE is_active = 1")['total'];
    
    // Total locations
    $totalLocations = $db->fetchOne("SELECT COUNT(*) as total FROM locations WHERE is_active = 1")['total'];
    
    // Total work schedules
    $totalSchedules = $db->fetchOne("SELECT COUNT(*) as total FROM work_schedules WHERE is_active = 1")['total'];
    
    // Today's attendance
    $todayAttendance = $db->fetchOne("SELECT COUNT(*) as total FROM attendance WHERE attendance_date = CURDATE()")['total'];
    
    // Recent attendance data
    $recentAttendance = $db->fetchAll("
        SELECT a.*, e.name as employee_name, e.nik, l.name as location_name
        FROM attendance a
        JOIN employees e ON a.employee_id = e.id
        LEFT JOIN locations l ON a.location_id = l.id
        ORDER BY a.created_at DESC
        LIMIT 10
    ");
    
    // Pending schedule swaps
    $pendingSwaps = $db->fetchOne("SELECT COUNT(*) as total FROM schedule_swaps WHERE status = 'pending'")['total'];
    
} catch (Exception $e) {
    $error = "Error loading dashboard data: " . $e->getMessage();
}
?>

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
        </h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Karyawan
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $totalEmployees ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Absen Hari Ini
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $todayAttendance ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Total Lokasi
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $totalLocations ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-map-marker-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Rolling Pending
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $pendingSwaps ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Attendance -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>Absensi Terbaru
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($recentAttendance)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Belum ada data absensi</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>NIK</th>
                                    <th>Nama Karyawan</th>
                                    <th>Tanggal</th>
                                    <th>Jam Masuk</th>
                                    <th>Jam Keluar</th>
                                    <th>Lokasi</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentAttendance as $attendance): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($attendance['nik']) ?></td>
                                        <td><?= htmlspecialchars($attendance['employee_name']) ?></td>
                                        <td><?= formatDate($attendance['attendance_date']) ?></td>
                                        <td>
                                            <?= $attendance['check_in_time'] ? formatDateTime($attendance['check_in_time'], 'H:i') : '-' ?>
                                        </td>
                                        <td>
                                            <?= $attendance['check_out_time'] ? formatDateTime($attendance['check_out_time'], 'H:i') : '-' ?>
                                        </td>
                                        <td><?= htmlspecialchars($attendance['location_name'] ?? '-') ?></td>
                                        <td>
                                            <?php
                                            $statusClass = [
                                                'present' => 'success',
                                                'late' => 'warning',
                                                'absent' => 'danger',
                                                'half_day' => 'info'
                                            ];
                                            $statusText = [
                                                'present' => 'Hadir',
                                                'late' => 'Terlambat',
                                                'absent' => 'Tidak Hadir',
                                                'half_day' => 'Setengah Hari'
                                            ];
                                            ?>
                                            <span class="badge bg-<?= $statusClass[$attendance['status']] ?>">
                                                <?= $statusText[$attendance['status']] ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="monitoring.php" class="btn btn-primary">
                            <i class="fas fa-eye me-2"></i>Lihat Semua Absensi
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
</style>

<?php include 'includes/footer.php'; ?>
