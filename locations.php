<?php
$page_title = 'Lokasi Absen';
include 'includes/header.php';

// Handle form submissions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    if ($action == 'add' || $action == 'edit') {
        $name = sanitizeInput($_POST['name']);
        $latitude = (float)$_POST['latitude'];
        $longitude = (float)$_POST['longitude'];
        $radius = (int)$_POST['radius'];
        $address = sanitizeInput($_POST['address']);
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        
        // Validation
        $errors = [];
        if (empty($name)) $errors[] = 'Nama lokasi harus diisi';
        if ($latitude == 0) $errors[] = 'Latitude harus diisi';
        if ($longitude == 0) $errors[] = 'Longitude harus diisi';
        if ($radius <= 0) $errors[] = 'Radius harus lebih dari 0';
        if ($latitude < -90 || $latitude > 90) $errors[] = 'Latitude harus antara -90 dan 90';
        if ($longitude < -180 || $longitude > 180) $errors[] = 'Longitude harus antara -180 dan 180';
        
        if (empty($errors)) {
            try {
                $data = [
                    'name' => $name,
                    'latitude' => $latitude,
                    'longitude' => $longitude,
                    'radius' => $radius,
                    'address' => $address,
                    'is_active' => $is_active
                ];
                
                if ($action == 'add') {
                    $db->insert('locations', $data);
                    logActivity('ADD_LOCATION', "Added location: {$name}");
                    setAlert('success', 'Lokasi berhasil ditambahkan');
                } else {
                    $id = (int)$_POST['id'];
                    $db->update('locations', $data, 'id = ?', [$id]);
                    logActivity('EDIT_LOCATION', "Updated location: {$name}");
                    setAlert('success', 'Lokasi berhasil diperbarui');
                }
            } catch (Exception $e) {
                setAlert('danger', 'Terjadi kesalahan: ' . $e->getMessage());
            }
        } else {
            setAlert('danger', implode('<br>', $errors));
        }
        
        header('Location: locations.php');
        exit();
    }
    
    if ($action == 'delete') {
        $id = (int)$_POST['id'];
        try {
            // Check if location is being used
            $usage = $db->fetchOne("SELECT COUNT(*) as total FROM employee_schedules WHERE location_id = ?", [$id]);
            if ($usage['total'] > 0) {
                setAlert('danger', 'Lokasi tidak dapat dihapus karena sedang digunakan oleh karyawan');
            } else {
                $location = $db->fetchOne("SELECT name FROM locations WHERE id = ?", [$id]);
                if ($location) {
                    $db->delete('locations', 'id = ?', [$id]);
                    logActivity('DELETE_LOCATION', "Deleted location: {$location['name']}");
                    setAlert('success', 'Lokasi berhasil dihapus');
                }
            }
        } catch (Exception $e) {
            setAlert('danger', 'Terjadi kesalahan: ' . $e->getMessage());
        }
        
        header('Location: locations.php');
        exit();
    }
}

// Get locations data
try {
    $locations = $db->fetchAll("
        SELECT l.*, 
               (SELECT COUNT(*) FROM employee_schedules es WHERE es.location_id = l.id AND es.is_active = 1) as employee_count
        FROM locations l
        ORDER BY l.created_at DESC
    ");
} catch (Exception $e) {
    $error = "Error loading data: " . $e->getMessage();
}
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-map-marker-alt me-2"></i>Lokasi Absen
            </h1>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#locationModal" onclick="resetForm()">
                <i class="fas fa-plus me-2"></i>Tambah Lokasi
            </button>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <?php if (isset($error)): ?>
                    <div class="alert alert-danger"><?= $error ?></div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover data-table">
                            <thead>
                                <tr>
                                    <th>Nama Lokasi</th>
                                    <th>Koordinat</th>
                                    <th>Radius</th>
                                    <th>Alamat</th>
                                    <th>Karyawan</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($locations as $location): ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($location['name']) ?></strong>
                                        </td>
                                        <td>
                                            <small>
                                                Lat: <?= $location['latitude'] ?><br>
                                                Lng: <?= $location['longitude'] ?>
                                            </small>
                                            <br>
                                            <button type="button" class="btn btn-sm btn-outline-info mt-1" 
                                                    onclick="showMap(<?= $location['latitude'] ?>, <?= $location['longitude'] ?>, '<?= htmlspecialchars($location['name']) ?>')">
                                                <i class="fas fa-map me-1"></i>Lihat Peta
                                            </button>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?= $location['radius'] ?> meter</span>
                                        </td>
                                        <td>
                                            <?= htmlspecialchars($location['address'] ?: '-') ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?= $location['employee_count'] ?> orang</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $location['is_active'] ? 'success' : 'danger' ?>">
                                                <?= $location['is_active'] ? 'Aktif' : 'Tidak Aktif' ?>
                                            </span>
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-warning" 
                                                    onclick="editLocation(<?= htmlspecialchars(json_encode($location)) ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <?php if ($location['employee_count'] == 0): ?>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        onclick="deleteLocation(<?= $location['id'] ?>, '<?= htmlspecialchars($location['name']) ?>')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            <?php else: ?>
                                                <button type="button" class="btn btn-sm btn-danger" disabled title="Tidak dapat dihapus karena sedang digunakan">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Location Modal -->
<div class="modal fade" id="locationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">Tambah Lokasi</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" id="formAction" value="add">
                    <input type="hidden" name="id" id="locationId">
                    
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="name" class="form-label">Nama Lokasi *</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                            <div class="invalid-feedback">Nama lokasi harus diisi</div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="radius" class="form-label">Radius (meter) *</label>
                            <input type="number" class="form-control" id="radius" name="radius" min="1" max="1000" required>
                            <div class="invalid-feedback">Radius harus diisi (1-1000 meter)</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="latitude" class="form-label">Latitude *</label>
                            <input type="number" class="form-control" id="latitude" name="latitude" 
                                   step="0.000001" min="-90" max="90" required>
                            <div class="invalid-feedback">Latitude harus diisi (-90 sampai 90)</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="longitude" class="form-label">Longitude *</label>
                            <input type="number" class="form-control" id="longitude" name="longitude" 
                                   step="0.000001" min="-180" max="180" required>
                            <div class="invalid-feedback">Longitude harus diisi (-180 sampai 180)</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="address" class="form-label">Alamat</label>
                            <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                <label class="form-check-label" for="is_active">
                                    Status Aktif
                                </label>
                            </div>
                            <button type="button" class="btn btn-outline-success btn-sm mt-2" onclick="getCurrentLocation()">
                                <i class="fas fa-crosshairs me-1"></i>Lokasi Saat Ini
                            </button>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Tips:</strong> Anda dapat menggunakan Google Maps untuk mendapatkan koordinat yang akurat. 
                        Klik kanan pada lokasi di Google Maps dan pilih koordinat yang muncul.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Map Modal -->
<div class="modal fade" id="mapModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="mapModalTitle">Lokasi di Peta</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="map" style="height: 400px; width: 100%;"></div>
            </div>
        </div>
    </div>
</div>

<?php
$additional_js = "
<script>
function resetForm() {
    document.getElementById('modalTitle').textContent = 'Tambah Lokasi';
    document.getElementById('formAction').value = 'add';
    document.getElementById('locationId').value = '';
    document.querySelector('#locationModal form').reset();
    document.querySelector('#locationModal form').classList.remove('was-validated');
    document.getElementById('is_active').checked = true;
}

function editLocation(location) {
    document.getElementById('modalTitle').textContent = 'Edit Lokasi';
    document.getElementById('formAction').value = 'edit';
    document.getElementById('locationId').value = location.id;
    document.getElementById('name').value = location.name;
    document.getElementById('latitude').value = location.latitude;
    document.getElementById('longitude').value = location.longitude;
    document.getElementById('radius').value = location.radius;
    document.getElementById('address').value = location.address || '';
    document.getElementById('is_active').checked = location.is_active == 1;
    
    new bootstrap.Modal(document.getElementById('locationModal')).show();
}

function deleteLocation(id, name) {
    if (confirmDelete('Apakah Anda yakin ingin menghapus lokasi \"' + name + '\"?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"delete\"><input type=\"hidden\" name=\"id\" value=\"' + id + '\">';
        document.body.appendChild(form);
        form.submit();
    }
}

function getCurrentLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
            document.getElementById('latitude').value = position.coords.latitude.toFixed(6);
            document.getElementById('longitude').value = position.coords.longitude.toFixed(6);
            alert('Lokasi berhasil didapatkan!');
        }, function(error) {
            alert('Gagal mendapatkan lokasi: ' + error.message);
        });
    } else {
        alert('Browser tidak mendukung geolocation');
    }
}

function showMap(lat, lng, name) {
    document.getElementById('mapModalTitle').textContent = 'Lokasi: ' + name;
    
    // Simple map using Google Maps embed (fallback)
    const mapDiv = document.getElementById('map');
    mapDiv.innerHTML = '<iframe width=\"100%\" height=\"400\" frameborder=\"0\" style=\"border:0\" src=\"https://www.google.com/maps?q=' + lat + ',' + lng + '&z=15&output=embed\"></iframe>';
    
    new bootstrap.Modal(document.getElementById('mapModal')).show();
}
</script>
";
?>

<?php include 'includes/footer.php'; ?>
