{"name": "absensi-pdam-mobile", "version": "1.0.0", "description": "Aplikasi Mobile Absensi PDAM - Ionic React", "main": "index.js", "scripts": {"build": "ionic build", "start": "ionic serve", "dev": "ionic serve --lab", "android": "ionic capacitor run android", "ios": "ionic capacitor run ios", "sync": "ionic capacitor sync", "copy": "ionic capacitor copy", "build:android": "ionic build && ionic capacitor copy android && ionic capacitor run android", "build:ios": "ionic build && ionic capacitor copy ios && ionic capacitor run ios", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"@capacitor/android": "^5.0.0", "@capacitor/app": "^5.0.0", "@capacitor/camera": "^5.0.0", "@capacitor/core": "^5.0.0", "@capacitor/geolocation": "^5.0.0", "@capacitor/haptics": "^5.0.0", "@capacitor/ios": "^5.0.0", "@capacitor/keyboard": "^5.0.0", "@capacitor/network": "^5.0.0", "@capacitor/preferences": "^5.0.0", "@capacitor/splash-screen": "^5.0.0", "@capacitor/status-bar": "^5.0.0", "@ionic/react": "^7.0.0", "@ionic/react-router": "^7.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/react-router": "^5.1.0", "@types/react-router-dom": "^5.3.0", "axios": "^1.4.0", "date-fns": "^2.30.0", "ionicons": "^7.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router": "^5.3.0", "react-router-dom": "^5.3.0", "react-scripts": "^5.0.1", "typescript": "^4.9.0", "web-vitals": "^3.0.0"}, "devDependencies": {"@capacitor/cli": "^5.0.0", "@ionic/cli": "^7.0.0", "@types/node": "^18.0.0", "eslint": "^8.0.0", "prettier": "^2.8.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "keywords": ["ionic", "react", "abs<PERSON><PERSON>", "pdam", "mobile", "attendance"], "author": "PDAM Development Team", "license": "MIT"}