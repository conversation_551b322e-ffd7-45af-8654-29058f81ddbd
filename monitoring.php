<?php
$page_title = 'Monitoring Absensi';
include 'includes/header.php';

// Get filter parameters
$filter_date = $_GET['date'] ?? date('Y-m-d');
$filter_department = $_GET['department'] ?? '';
$filter_status = $_GET['status'] ?? '';

// Build WHERE clause for filters
$whereConditions = ["a.attendance_date = ?"];
$params = [$filter_date];

if (!empty($filter_department)) {
    $whereConditions[] = "e.department_id = ?";
    $params[] = $filter_department;
}

if (!empty($filter_status)) {
    $whereConditions[] = "a.status = ?";
    $params[] = $filter_status;
}

$whereClause = implode(' AND ', $whereConditions);

try {
    // Get attendance data with filters
    $attendances = $db->fetchAll("
        SELECT a.*, e.name as employee_name, e.nik,
               d.name as department_name, p.name as position_name,
               l.name as location_name, ws.name as schedule_name,
               ws.start_time, ws.end_time, ws.late_check_in_limit,
               a.check_in_photo, a.check_out_photo
        FROM attendance a
        JOIN employees e ON a.employee_id = e.id
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN positions p ON e.position_id = p.id
        LEFT JOIN locations l ON a.location_id = l.id
        LEFT JOIN work_schedules ws ON a.work_schedule_id = ws.id
        WHERE {$whereClause}
        ORDER BY a.check_in_time DESC, e.name ASC
    ", $params);
    
    // Get employees who haven't checked in yet
    $absentEmployees = $db->fetchAll("
        SELECT e.*, d.name as department_name, p.name as position_name,
               es.work_schedule_id, ws.name as schedule_name, ws.start_time, ws.end_time
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN positions p ON e.position_id = p.id
        LEFT JOIN employee_schedules es ON e.id = es.employee_id AND es.is_active = 1
        LEFT JOIN work_schedules ws ON es.work_schedule_id = ws.id
        LEFT JOIN attendance a ON e.id = a.employee_id AND a.attendance_date = ?
        WHERE e.is_active = 1 AND a.id IS NULL
        " . (!empty($filter_department) ? " AND e.department_id = {$filter_department}" : "") . "
        ORDER BY e.name
    ", [$filter_date]);
    
    // Get departments for filter
    $departments = $db->fetchAll("SELECT * FROM departments ORDER BY name");
    
    // Calculate statistics
    $totalEmployees = count($attendances) + count($absentEmployees);
    $presentCount = count(array_filter($attendances, function($a) { return $a['check_in_time']; }));
    $lateCount = count(array_filter($attendances, function($a) { return $a['status'] == 'late'; }));
    $absentCount = count($absentEmployees);
    
} catch (Exception $e) {
    $error = "Error loading data: " . $e->getMessage();
}
?>

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-eye me-2"></i>Monitoring Absensi
            <small class="text-muted">- <?= formatDate($filter_date) ?></small>
        </h1>
    </div>
</div>

<!-- Filter Form -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="date" class="form-label">Tanggal</label>
                        <input type="date" class="form-control" id="date" name="date" value="<?= $filter_date ?>">
                    </div>
                    <div class="col-md-3">
                        <label for="department" class="form-label">Departemen</label>
                        <select class="form-select" id="department" name="department">
                            <option value="">Semua Departemen</option>
                            <?php foreach ($departments as $dept): ?>
                                <option value="<?= $dept['id'] ?>" <?= $filter_department == $dept['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($dept['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">Semua Status</option>
                            <option value="present" <?= $filter_status == 'present' ? 'selected' : '' ?>>Hadir</option>
                            <option value="late" <?= $filter_status == 'late' ? 'selected' : '' ?>>Terlambat</option>
                            <option value="half_day" <?= $filter_status == 'half_day' ? 'selected' : '' ?>>Setengah Hari</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Filter
                            </button>
                            <a href="monitoring.php" class="btn btn-outline-secondary">
                                <i class="fas fa-refresh me-2"></i>Reset
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Karyawan</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $totalEmployees ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Hadir</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $presentCount ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Terlambat</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $lateCount ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Tidak Hadir</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $absentCount ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-times fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Attendance Data -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>Data Absensi
                    <button type="button" class="btn btn-sm btn-outline-primary float-end" onclick="refreshData()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                </h5>
            </div>
            <div class="card-body">
                <?php if (isset($error)): ?>
                    <div class="alert alert-danger"><?= $error ?></div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>NIK</th>
                                    <th>Nama</th>
                                    <th>Departemen</th>
                                    <th>Jadwal</th>
                                    <th>Jam Masuk</th>
                                    <th>Jam Keluar</th>
                                    <th>Foto</th>
                                    <th>Lokasi</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Present Employees -->
                                <?php foreach ($attendances as $attendance): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($attendance['nik']) ?></td>
                                        <td>
                                            <strong><?= htmlspecialchars($attendance['employee_name']) ?></strong>
                                            <br><small class="text-muted"><?= htmlspecialchars($attendance['position_name'] ?? '') ?></small>
                                        </td>
                                        <td><?= htmlspecialchars($attendance['department_name'] ?? '-') ?></td>
                                        <td>
                                            <small>
                                                <?= htmlspecialchars($attendance['schedule_name'] ?? '-') ?><br>
                                                <?= $attendance['start_time'] ? formatTime($attendance['start_time']) . '-' . formatTime($attendance['end_time']) : '-' ?>
                                            </small>
                                        </td>
                                        <td>
                                            <?php if ($attendance['check_in_time']): ?>
                                                <strong><?= formatDateTime($attendance['check_in_time'], 'H:i') ?></strong>
                                                <?php if ($attendance['status'] == 'late'): ?>
                                                    <br><small class="text-danger">Terlambat</small>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?= $attendance['check_out_time'] ? formatDateTime($attendance['check_out_time'], 'H:i') : '<span class="text-muted">-</span>' ?>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column gap-1">
                                                <!-- Foto Absen Masuk -->
                                                <div class="d-flex align-items-center">
                                                    <small class="text-muted me-2" style="min-width: 60px;">Masuk:</small>
                                                    <?php if ($attendance['check_in_photo']): ?>
                                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                                                onclick="viewPhoto('<?= htmlspecialchars($attendance['check_in_photo']) ?>', 'Absen Masuk - <?= htmlspecialchars($attendance['employee_name']) ?>')"
                                                                title="Lihat Foto Absen Masuk">
                                                            <i class="fas fa-camera me-1"></i>Lihat Foto
                                                        </button>
                                                    <?php else: ?>
                                                        <span class="text-muted">Tidak ada foto</span>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- Foto Absen Pulang -->
                                                <div class="d-flex align-items-center">
                                                    <small class="text-muted me-2" style="min-width: 60px;">Pulang:</small>
                                                    <?php if ($attendance['check_out_photo']): ?>
                                                        <button type="button" class="btn btn-sm btn-outline-success"
                                                                onclick="viewPhoto('<?= htmlspecialchars($attendance['check_out_photo']) ?>', 'Absen Pulang - <?= htmlspecialchars($attendance['employee_name']) ?>')"
                                                                title="Lihat Foto Absen Pulang">
                                                            <i class="fas fa-camera me-1"></i>Lihat Foto
                                                        </button>
                                                    <?php elseif ($attendance['check_in_time'] && !$attendance['check_out_time']): ?>
                                                        <span class="text-warning">Belum absen pulang</span>
                                                    <?php else: ?>
                                                        <span class="text-muted">Tidak ada foto</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?= htmlspecialchars($attendance['location_name'] ?? '-') ?></td>
                                        <td>
                                            <?php
                                            $statusClass = [
                                                'present' => 'success',
                                                'late' => 'warning',
                                                'absent' => 'danger',
                                                'half_day' => 'info'
                                            ];
                                            $statusText = [
                                                'present' => 'Hadir',
                                                'late' => 'Terlambat',
                                                'absent' => 'Tidak Hadir',
                                                'half_day' => 'Setengah Hari'
                                            ];
                                            ?>
                                            <span class="badge bg-<?= $statusClass[$attendance['status']] ?>">
                                                <?= $statusText[$attendance['status']] ?>
                                            </span>
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-outline-info" 
                                                    onclick="viewDetails(<?= htmlspecialchars(json_encode($attendance)) ?>)">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                                
                                <!-- Absent Employees -->
                                <?php foreach ($absentEmployees as $employee): ?>
                                    <tr class="table-light">
                                        <td><?= htmlspecialchars($employee['nik']) ?></td>
                                        <td>
                                            <strong><?= htmlspecialchars($employee['name']) ?></strong>
                                            <br><small class="text-muted"><?= htmlspecialchars($employee['position_name'] ?? '') ?></small>
                                        </td>
                                        <td><?= htmlspecialchars($employee['department_name'] ?? '-') ?></td>
                                        <td>
                                            <small>
                                                <?= htmlspecialchars($employee['schedule_name'] ?? '-') ?><br>
                                                <?= $employee['start_time'] ? formatTime($employee['start_time']) . '-' . formatTime($employee['end_time']) : '-' ?>
                                            </small>
                                        </td>
                                        <td><span class="text-muted">Belum absen</span></td>
                                        <td><span class="text-muted">-</span></td>
                                        <td><span class="text-muted">-</span></td>
                                        <td><span class="text-muted">-</span></td>
                                        <td>
                                            <span class="badge bg-secondary">Belum Absen</span>
                                        </td>
                                        <td>
                                            <span class="text-muted">-</span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Detail Modal -->
<div class="modal fade" id="detailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Detail Absensi</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="detailContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Photo Modal -->
<div class="modal fade" id="photoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="photoModalTitle">Foto Absensi</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="photoImage" src="" alt="Foto Absensi" class="img-fluid" style="max-height: 500px;">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                <a id="downloadPhoto" href="" download class="btn btn-primary">
                    <i class="fas fa-download me-2"></i>Download
                </a>
            </div>
        </div>
    </div>
</div>

<?php
$additional_js = "
<script>
function refreshData() {
    location.reload();
}

function viewPhoto(filename, type) {
    const photoUrl = 'uploads/attendance_photos/' + filename;
    document.getElementById('photoModalTitle').textContent = 'Foto ' + type;
    document.getElementById('photoImage').src = photoUrl;
    document.getElementById('downloadPhoto').href = photoUrl;
    document.getElementById('downloadPhoto').download = filename;
    new bootstrap.Modal(document.getElementById('photoModal')).show();
}

function viewDetails(attendance) {
    const content = `
        <div class='row'>
            <div class='col-md-6'>
                <h6>Informasi Karyawan</h6>
                <table class='table table-sm'>
                    <tr><td><strong>NIK:</strong></td><td>\${attendance.nik}</td></tr>
                    <tr><td><strong>Nama:</strong></td><td>\${attendance.employee_name}</td></tr>
                    <tr><td><strong>Departemen:</strong></td><td>\${attendance.department_name || '-'}</td></tr>
                    <tr><td><strong>Jabatan:</strong></td><td>\${attendance.position_name || '-'}</td></tr>
                </table>
            </div>
            <div class='col-md-6'>
                <h6>Informasi Absensi</h6>
                <table class='table table-sm'>
                    <tr><td><strong>Tanggal:</strong></td><td>\${new Date(attendance.attendance_date).toLocaleDateString('id-ID')}</td></tr>
                    <tr><td><strong>Jam Masuk:</strong></td><td>\${attendance.check_in_time ? new Date(attendance.check_in_time).toLocaleTimeString('id-ID', {hour: '2-digit', minute: '2-digit'}) : '-'}</td></tr>
                    <tr><td><strong>Jam Keluar:</strong></td><td>\${attendance.check_out_time ? new Date(attendance.check_out_time).toLocaleTimeString('id-ID', {hour: '2-digit', minute: '2-digit'}) : '-'}</td></tr>
                    <tr><td><strong>Lokasi:</strong></td><td>\${attendance.location_name || '-'}</td></tr>
                    <tr><td><strong>Status:</strong></td><td>\${getStatusText(attendance.status)}</td></tr>
                </table>
            </div>
        </div>
        \${(attendance.check_in_photo || attendance.check_out_photo) ? `
        <div class='row mt-3'>
            <div class='col-12'>
                <h6><i class='fas fa-camera me-2'></i>Foto Validasi Absensi</h6>
                <div class='row'>
                    \${attendance.check_in_photo ? `
                        <div class='col-md-6'>
                            <div class='card'>
                                <div class='card-header bg-primary text-white text-center'>
                                    <small><i class='fas fa-sign-in-alt me-1'></i>Foto Absen Masuk</small>
                                </div>
                                <div class='card-body text-center p-2'>
                                    <img src='uploads/attendance_photos/\${attendance.check_in_photo}'
                                         alt='Foto Absen Masuk' class='img-fluid rounded'
                                         style='max-height: 200px; cursor: pointer;'
                                         onclick='viewPhoto(\"\${attendance.check_in_photo}\", \"Foto Absen Masuk - \${attendance.employee_name}\")'>
                                    <br><small class='text-muted mt-1'>Klik untuk memperbesar</small>
                                </div>
                            </div>
                        </div>
                    ` : `
                        <div class='col-md-6'>
                            <div class='card'>
                                <div class='card-header bg-secondary text-white text-center'>
                                    <small><i class='fas fa-sign-in-alt me-1'></i>Foto Absen Masuk</small>
                                </div>
                                <div class='card-body text-center p-3'>
                                    <i class='fas fa-camera-slash fa-3x text-muted mb-2'></i>
                                    <br><small class='text-muted'>Tidak ada foto</small>
                                </div>
                            </div>
                        </div>
                    `}
                    \${attendance.check_out_photo ? `
                        <div class='col-md-6'>
                            <div class='card'>
                                <div class='card-header bg-success text-white text-center'>
                                    <small><i class='fas fa-sign-out-alt me-1'></i>Foto Absen Pulang</small>
                                </div>
                                <div class='card-body text-center p-2'>
                                    <img src='uploads/attendance_photos/\${attendance.check_out_photo}'
                                         alt='Foto Absen Pulang' class='img-fluid rounded'
                                         style='max-height: 200px; cursor: pointer;'
                                         onclick='viewPhoto(\"\${attendance.check_out_photo}\", \"Foto Absen Pulang - \${attendance.employee_name}\")'>
                                    <br><small class='text-muted mt-1'>Klik untuk memperbesar</small>
                                </div>
                            </div>
                        </div>
                    ` : `
                        <div class='col-md-6'>
                            <div class='card'>
                                <div class='card-header bg-secondary text-white text-center'>
                                    <small><i class='fas fa-sign-out-alt me-1'></i>Foto Absen Pulang</small>
                                </div>
                                <div class='card-body text-center p-3'>
                                    \${attendance.check_in_time && !attendance.check_out_time ? `
                                        <i class='fas fa-clock fa-3x text-warning mb-2'></i>
                                        <br><small class='text-warning'>Belum absen pulang</small>
                                    ` : `
                                        <i class='fas fa-camera-slash fa-3x text-muted mb-2'></i>
                                        <br><small class='text-muted'>Tidak ada foto</small>
                                    `}
                                </div>
                            </div>
                        </div>
                    `}
                </div>
            </div>
        </div>
        ` : ''}
        \${attendance.check_in_latitude ? `
        <div class='row mt-3'>
            <div class='col-12'>
                <h6>Koordinat Absensi</h6>
                <table class='table table-sm'>
                    <tr><td><strong>Koordinat Masuk:</strong></td><td>\${attendance.check_in_latitude}, \${attendance.check_in_longitude}</td></tr>
                    \${attendance.check_out_latitude ? `<tr><td><strong>Koordinat Keluar:</strong></td><td>\${attendance.check_out_latitude}, \${attendance.check_out_longitude}</td></tr>` : ''}
                </table>
            </div>
        </div>
        ` : ''}
        \${attendance.notes ? `
        <div class='row mt-3'>
            <div class='col-12'>
                <h6>Catatan</h6>
                <p>\${attendance.notes}</p>
            </div>
        </div>
        ` : ''}
    `;
    
    document.getElementById('detailContent').innerHTML = content;
    new bootstrap.Modal(document.getElementById('detailModal')).show();
}

function getStatusText(status) {
    const statusText = {
        'present': 'Hadir',
        'late': 'Terlambat',
        'absent': 'Tidak Hadir',
        'half_day': 'Setengah Hari'
    };
    return statusText[status] || status;
}

// Auto refresh every 5 minutes
setInterval(function() {
    if (document.visibilityState === 'visible') {
        refreshData();
    }
}, 300000);
</script>
";
?>

<?php include 'includes/footer.php'; ?>
