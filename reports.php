<?php
$page_title = '<PERSON>poran Absen';
include 'includes/header.php';

// Get filter parameters
$filter_month = $_GET['month'] ?? date('Y-m');
$filter_department = $_GET['department'] ?? '';
$filter_employee = $_GET['employee'] ?? '';
$export = $_GET['export'] ?? '';

// Parse month and year
$year = date('Y', strtotime($filter_month . '-01'));
$month = date('m', strtotime($filter_month . '-01'));
$start_date = $year . '-' . $month . '-01';
$end_date = date('Y-m-t', strtotime($start_date));

// Build WHERE clause for filters
$whereConditions = ["a.attendance_date BETWEEN ? AND ?"];
$params = [$start_date, $end_date];

if (!empty($filter_department)) {
    $whereConditions[] = "e.department_id = ?";
    $params[] = $filter_department;
}

if (!empty($filter_employee)) {
    $whereConditions[] = "e.id = ?";
    $params[] = $filter_employee;
}

$whereClause = implode(' AND ', $whereConditions);

try {
    // Get attendance data with filters
    $attendances = $db->fetchAll("
        SELECT a.*, e.name as employee_name, e.nik, 
               d.name as department_name, p.name as position_name,
               l.name as location_name, ws.name as schedule_name
        FROM attendance a
        JOIN employees e ON a.employee_id = e.id
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN positions p ON e.position_id = p.id
        LEFT JOIN locations l ON a.location_id = l.id
        LEFT JOIN work_schedules ws ON a.work_schedule_id = ws.id
        WHERE {$whereClause}
        ORDER BY e.name ASC, a.attendance_date ASC
    ", $params);
    
    // Get all employees for the period (including those who didn't attend)
    $employees = $db->fetchAll("
        SELECT e.*, d.name as department_name, p.name as position_name
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN positions p ON e.position_id = p.id
        WHERE e.is_active = 1
        " . (!empty($filter_department) ? " AND e.department_id = {$filter_department}" : "") . "
        " . (!empty($filter_employee) ? " AND e.id = {$filter_employee}" : "") . "
        ORDER BY e.name
    ");
    
    // Get departments for filter
    $departments = $db->fetchAll("SELECT * FROM departments ORDER BY name");
    
    // Get employees for filter
    $allEmployees = $db->fetchAll("SELECT * FROM employees WHERE is_active = 1 ORDER BY name");
    
    // Process data for report
    $reportData = [];
    $totalDays = date('t', strtotime($start_date));
    
    foreach ($employees as $employee) {
        $employeeAttendances = array_filter($attendances, function($a) use ($employee) {
            return $a['employee_id'] == $employee['id'];
        });
        
        $presentDays = 0;
        $lateDays = 0;
        $absentDays = 0;
        $halfDays = 0;
        
        // Create attendance array for each day
        $dailyAttendance = [];
        for ($day = 1; $day <= $totalDays; $day++) {
            $date = $year . '-' . $month . '-' . sprintf('%02d', $day);
            $dayAttendance = array_filter($employeeAttendances, function($a) use ($date) {
                return $a['attendance_date'] == $date;
            });
            
            if (!empty($dayAttendance)) {
                $attendance = reset($dayAttendance);
                $dailyAttendance[$day] = $attendance['status'];
                
                switch ($attendance['status']) {
                    case 'present': $presentDays++; break;
                    case 'late': $lateDays++; break;
                    case 'half_day': $halfDays++; break;
                }
            } else {
                // Check if it's weekend (assuming Saturday and Sunday are weekends)
                $dayOfWeek = date('w', strtotime($date));
                if ($dayOfWeek == 0 || $dayOfWeek == 6) {
                    $dailyAttendance[$day] = 'weekend';
                } else {
                    $dailyAttendance[$day] = 'absent';
                    $absentDays++;
                }
            }
        }
        
        $weekendDays = array_count_values($dailyAttendance)['weekend'] ?? 0;
        $workDays = $totalDays - $weekendDays;
        
        $reportData[] = [
            'employee' => $employee,
            'daily_attendance' => $dailyAttendance,
            'present_days' => $presentDays,
            'late_days' => $lateDays,
            'absent_days' => $absentDays,
            'half_days' => $halfDays,
            'total_work_days' => $presentDays + $lateDays + $halfDays,
            'attendance_percentage' => $workDays > 0 ? round(($presentDays + $lateDays + $halfDays) / $workDays * 100, 1) : 0
        ];
    }
    
    // Handle export
    if ($export == 'excel') {
        exportToExcel($reportData, $filter_month, $totalDays);
        exit();
    }
    
} catch (Exception $e) {
    $error = "Error loading data: " . $e->getMessage();
}

function exportToExcel($reportData, $month, $totalDays) {
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment;filename="laporan_absensi_' . $month . '.xls"');
    header('Cache-Control: max-age=0');
    
    echo "<table border='1'>";
    echo "<tr>";
    echo "<th colspan='" . (6 + $totalDays) . "'>LAPORAN ABSENSI BULAN " . strtoupper(date('F Y', strtotime($month . '-01'))) . "</th>";
    echo "</tr>";
    
    echo "<tr>";
    echo "<th>NIK</th>";
    echo "<th>Nama</th>";
    echo "<th>Departemen</th>";
    echo "<th>Jabatan</th>";
    
    for ($day = 1; $day <= $totalDays; $day++) {
        echo "<th>{$day}</th>";
    }
    
    echo "<th>Hadir</th>";
    echo "<th>Terlambat</th>";
    echo "<th>Tidak Hadir</th>";
    echo "<th>Persentase</th>";
    echo "</tr>";
    
    foreach ($reportData as $data) {
        echo "<tr>";
        echo "<td>" . $data['employee']['nik'] . "</td>";
        echo "<td>" . $data['employee']['name'] . "</td>";
        echo "<td>" . $data['employee']['department_name'] . "</td>";
        echo "<td>" . $data['employee']['position_name'] . "</td>";
        
        for ($day = 1; $day <= $totalDays; $day++) {
            $status = $data['daily_attendance'][$day] ?? 'absent';
            $symbol = '';
            switch ($status) {
                case 'present': $symbol = 'H'; break;
                case 'late': $symbol = 'T'; break;
                case 'absent': $symbol = 'A'; break;
                case 'half_day': $symbol = 'S'; break;
                case 'weekend': $symbol = 'L'; break;
            }
            echo "<td>{$symbol}</td>";
        }
        
        echo "<td>" . $data['present_days'] . "</td>";
        echo "<td>" . $data['late_days'] . "</td>";
        echo "<td>" . $data['absent_days'] . "</td>";
        echo "<td>" . $data['attendance_percentage'] . "%</td>";
        echo "</tr>";
    }
    
    echo "</table>";
}
?>

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-chart-bar me-2"></i>Laporan Absen
            <small class="text-muted">- <?= date('F Y', strtotime($filter_month . '-01')) ?></small>
        </h1>
    </div>
</div>

<!-- Filter Form -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="month" class="form-label">Bulan</label>
                        <input type="month" class="form-control" id="month" name="month" value="<?= $filter_month ?>">
                    </div>
                    <div class="col-md-3">
                        <label for="department" class="form-label">Departemen</label>
                        <select class="form-select" id="department" name="department">
                            <option value="">Semua Departemen</option>
                            <?php foreach ($departments as $dept): ?>
                                <option value="<?= $dept['id'] ?>" <?= $filter_department == $dept['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($dept['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="employee" class="form-label">Karyawan</label>
                        <select class="form-select" id="employee" name="employee">
                            <option value="">Semua Karyawan</option>
                            <?php foreach ($allEmployees as $emp): ?>
                                <option value="<?= $emp['id'] ?>" <?= $filter_employee == $emp['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($emp['name']) ?> (<?= htmlspecialchars($emp['nik']) ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Filter
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Export and Summary -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <h6>Ringkasan Laporan</h6>
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-primary"><?= count($reportData) ?></h4>
                            <small>Total Karyawan</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-success"><?= array_sum(array_column($reportData, 'present_days')) ?></h4>
                            <small>Total Hadir</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-warning"><?= array_sum(array_column($reportData, 'late_days')) ?></h4>
                            <small>Total Terlambat</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-danger"><?= array_sum(array_column($reportData, 'absent_days')) ?></h4>
                            <small>Total Tidak Hadir</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h6>Export Laporan</h6>
                <a href="?<?= http_build_query(array_merge($_GET, ['export' => 'excel'])) ?>" class="btn btn-success">
                    <i class="fas fa-file-excel me-2"></i>Export Excel
                </a>
                <button type="button" class="btn btn-info" onclick="window.print()">
                    <i class="fas fa-print me-2"></i>Print
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Report Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>Detail Laporan Absensi
                </h5>
            </div>
            <div class="card-body">
                <?php if (isset($error)): ?>
                    <div class="alert alert-danger"><?= $error ?></div>
                <?php elseif (empty($reportData)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Tidak ada data untuk periode yang dipilih</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-bordered table-sm">
                            <thead class="table-dark">
                                <tr>
                                    <th rowspan="2">NIK</th>
                                    <th rowspan="2">Nama</th>
                                    <th rowspan="2">Dept</th>
                                    <th colspan="<?= $totalDays ?>">Tanggal</th>
                                    <th rowspan="2">H</th>
                                    <th rowspan="2">T</th>
                                    <th rowspan="2">A</th>
                                    <th rowspan="2">%</th>
                                </tr>
                                <tr>
                                    <?php for ($day = 1; $day <= $totalDays; $day++): ?>
                                        <th class="text-center" style="min-width: 25px;"><?= $day ?></th>
                                    <?php endfor; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($reportData as $data): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($data['employee']['nik']) ?></td>
                                        <td>
                                            <strong><?= htmlspecialchars($data['employee']['name']) ?></strong>
                                            <br><small class="text-muted"><?= htmlspecialchars($data['employee']['position_name'] ?? '') ?></small>
                                        </td>
                                        <td><?= htmlspecialchars($data['employee']['department_name'] ?? '') ?></td>

                                        <?php for ($day = 1; $day <= $totalDays; $day++): ?>
                                            <?php
                                            $status = $data['daily_attendance'][$day] ?? 'absent';
                                            $class = '';
                                            $symbol = '';
                                            switch ($status) {
                                                case 'present':
                                                    $class = 'bg-success text-white';
                                                    $symbol = 'H';
                                                    break;
                                                case 'late':
                                                    $class = 'bg-warning';
                                                    $symbol = 'T';
                                                    break;
                                                case 'absent':
                                                    $class = 'bg-danger text-white';
                                                    $symbol = 'A';
                                                    break;
                                                case 'half_day':
                                                    $class = 'bg-info text-white';
                                                    $symbol = 'S';
                                                    break;
                                                case 'weekend':
                                                    $class = 'bg-secondary text-white';
                                                    $symbol = 'L';
                                                    break;
                                            }
                                            ?>
                                            <td class="text-center <?= $class ?>" style="font-size: 12px; font-weight: bold;">
                                                <?= $symbol ?>
                                            </td>
                                        <?php endfor; ?>

                                        <td class="text-center"><strong><?= $data['present_days'] ?></strong></td>
                                        <td class="text-center"><strong><?= $data['late_days'] ?></strong></td>
                                        <td class="text-center"><strong><?= $data['absent_days'] ?></strong></td>
                                        <td class="text-center">
                                            <strong class="<?= $data['attendance_percentage'] >= 80 ? 'text-success' : 'text-danger' ?>">
                                                <?= $data['attendance_percentage'] ?>%
                                            </strong>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-3">
                        <h6>Keterangan:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <span class="badge bg-success me-2">H</span> Hadir<br>
                                <span class="badge bg-warning me-2">T</span> Terlambat<br>
                            </div>
                            <div class="col-md-6">
                                <span class="badge bg-danger me-2">A</span> Tidak Hadir<br>
                                <span class="badge bg-info me-2">S</span> Setengah Hari<br>
                                <span class="badge bg-secondary me-2">L</span> Libur/Weekend
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .card-header, .btn, .form-control, .form-select, .alert {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .table {
        font-size: 10px !important;
    }

    .table th, .table td {
        padding: 2px !important;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
