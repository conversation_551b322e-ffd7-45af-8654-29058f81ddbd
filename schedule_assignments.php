<?php
$page_title = 'Assignment Jadwal Karyawan';
include 'includes/header.php';

// Handle form submissions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    if ($action == 'bulk_assign') {
        $employeeIds = $_POST['employee_ids'] ?? [];
        $workScheduleId = (int)$_POST['work_schedule_id'];
        $locationId = (int)$_POST['location_id'];
        $startDate = $_POST['start_date'];
        $endDate = $_POST['end_date'] ?: null;
        
        // Validation
        $errors = [];
        if (empty($employeeIds)) $errors[] = 'Pilih minimal satu karyawan';
        if ($workScheduleId <= 0) $errors[] = 'Jadwal kerja harus dipilih';
        if ($locationId <= 0) $errors[] = 'Lokasi harus dipilih';
        if (empty($startDate)) $errors[] = 'Tanggal mulai harus diisi';
        
        if (empty($errors)) {
            try {
                $db->beginTransaction();
                
                $successCount = 0;
                foreach ($employeeIds as $employeeId) {
                    $employeeId = (int)$employeeId;
                    
                    // Deactivate current active schedule
                    $db->query(
                        "UPDATE employee_schedules SET is_active = 0 WHERE employee_id = ? AND is_active = 1",
                        [$employeeId]
                    );
                    
                    // Insert new schedule
                    $data = [
                        'employee_id' => $employeeId,
                        'work_schedule_id' => $workScheduleId,
                        'location_id' => $locationId,
                        'start_date' => $startDate,
                        'end_date' => $endDate,
                        'is_active' => 1
                    ];
                    
                    $db->insert('employee_schedules', $data);
                    $successCount++;
                }
                
                $db->commit();
                logActivity('BULK_ASSIGN_SCHEDULE', "Bulk assigned schedule to {$successCount} employees");
                setAlert('success', "Berhasil mengatur jadwal untuk {$successCount} karyawan");
                
            } catch (Exception $e) {
                $db->rollback();
                setAlert('danger', 'Terjadi kesalahan: ' . $e->getMessage());
            }
        } else {
            setAlert('danger', implode('<br>', $errors));
        }
        
        header('Location: schedule_assignments.php');
        exit();
    }
}

// Get filter parameters
$filterDepartment = $_GET['department'] ?? '';
$filterSchedule = $_GET['schedule'] ?? '';

// Build WHERE clause for filters
$whereConditions = ["e.is_active = 1"];
$params = [];

if (!empty($filterDepartment)) {
    $whereConditions[] = "e.department_id = ?";
    $params[] = $filterDepartment;
}

if (!empty($filterSchedule)) {
    if ($filterSchedule == 'no_schedule') {
        $whereConditions[] = "es.id IS NULL";
    } else {
        $whereConditions[] = "es.work_schedule_id = ?";
        $params[] = $filterSchedule;
    }
}

$whereClause = implode(' AND ', $whereConditions);

try {
    // Get employees with current schedule
    $employees = $db->fetchAll("
        SELECT e.*, d.name as department_name, p.name as position_name,
               ws.name as current_schedule_name, ws.start_time, ws.end_time,
               l.name as current_location_name,
               es.start_date as schedule_start_date, es.end_date as schedule_end_date
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN positions p ON e.position_id = p.id
        LEFT JOIN employee_schedules es ON e.id = es.employee_id AND es.is_active = 1 
            AND (es.end_date IS NULL OR es.end_date >= CURDATE())
        LEFT JOIN work_schedules ws ON es.work_schedule_id = ws.id
        LEFT JOIN locations l ON es.location_id = l.id
        WHERE {$whereClause}
        ORDER BY d.name, e.name
    ", $params);
    
    // Get data for filters and forms
    $departments = $db->fetchAll("SELECT * FROM departments ORDER BY name");
    $workSchedules = $db->fetchAll("SELECT * FROM work_schedules WHERE is_active = 1 ORDER BY name");
    $locations = $db->fetchAll("SELECT * FROM locations WHERE is_active = 1 ORDER BY name");
    
} catch (Exception $e) {
    $error = "Error loading data: " . $e->getMessage();
}
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-calendar-alt me-2"></i>Assignment Jadwal Karyawan
            </h1>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#bulkAssignModal">
                <i class="fas fa-users-cog me-2"></i>Assignment Massal
            </button>
        </div>
    </div>
</div>

<!-- Filter Form -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="department" class="form-label">Departemen</label>
                        <select class="form-select" id="department" name="department">
                            <option value="">Semua Departemen</option>
                            <?php foreach ($departments as $dept): ?>
                                <option value="<?= $dept['id'] ?>" <?= $filterDepartment == $dept['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($dept['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="schedule" class="form-label">Jadwal Kerja</label>
                        <select class="form-select" id="schedule" name="schedule">
                            <option value="">Semua Jadwal</option>
                            <option value="no_schedule" <?= $filterSchedule == 'no_schedule' ? 'selected' : '' ?>>
                                Belum Ada Jadwal
                            </option>
                            <?php foreach ($workSchedules as $ws): ?>
                                <option value="<?= $ws['id'] ?>" <?= $filterSchedule == $ws['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($ws['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Filter
                            </button>
                            <a href="schedule_assignments.php" class="btn btn-outline-secondary">
                                <i class="fas fa-refresh me-2"></i>Reset
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Statistics -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card border-start border-primary border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small text-primary text-uppercase fw-bold mb-1">Total Karyawan</div>
                        <div class="h5 mb-0 fw-bold"><?= count($employees) ?></div>
                    </div>
                    <div class="ms-3">
                        <i class="fas fa-users fa-2x text-muted"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card border-start border-success border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small text-success text-uppercase fw-bold mb-1">Sudah Ada Jadwal</div>
                        <div class="h5 mb-0 fw-bold">
                            <?= count(array_filter($employees, function($e) { return !empty($e['current_schedule_name']); })) ?>
                        </div>
                    </div>
                    <div class="ms-3">
                        <i class="fas fa-check fa-2x text-muted"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card border-start border-warning border-4">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="small text-warning text-uppercase fw-bold mb-1">Belum Ada Jadwal</div>
                        <div class="h5 mb-0 fw-bold">
                            <?= count(array_filter($employees, function($e) { return empty($e['current_schedule_name']); })) ?>
                        </div>
                    </div>
                    <div class="ms-3">
                        <i class="fas fa-exclamation-triangle fa-2x text-muted"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Employee List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <?php if (isset($error)): ?>
                    <div class="alert alert-danger"><?= $error ?></div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover data-table">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    <th>NIK</th>
                                    <th>Nama</th>
                                    <th>Departemen</th>
                                    <th>Jadwal Saat Ini</th>
                                    <th>Lokasi</th>
                                    <th>Periode</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($employees as $employee): ?>
                                    <tr>
                                        <td>
                                            <input type="checkbox" name="employee_checkbox" 
                                                   value="<?= $employee['id'] ?>" class="form-check-input employee-checkbox">
                                        </td>
                                        <td><?= htmlspecialchars($employee['nik']) ?></td>
                                        <td>
                                            <strong><?= htmlspecialchars($employee['name']) ?></strong>
                                            <br><small class="text-muted"><?= htmlspecialchars($employee['position_name'] ?? '') ?></small>
                                        </td>
                                        <td><?= htmlspecialchars($employee['department_name'] ?? '-') ?></td>
                                        <td>
                                            <?php if ($employee['current_schedule_name']): ?>
                                                <strong><?= htmlspecialchars($employee['current_schedule_name']) ?></strong><br>
                                                <small class="text-muted">
                                                    <?= formatTime($employee['start_time']) ?> - <?= formatTime($employee['end_time']) ?>
                                                </small>
                                            <?php else: ?>
                                                <span class="text-danger">Belum ada jadwal</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= htmlspecialchars($employee['current_location_name'] ?? '-') ?></td>
                                        <td>
                                            <?php if ($employee['schedule_start_date']): ?>
                                                <?= formatDate($employee['schedule_start_date']) ?><br>
                                                <small class="text-muted">
                                                    s/d <?= $employee['schedule_end_date'] ? formatDate($employee['schedule_end_date']) : 'Sekarang' ?>
                                                </small>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="employee_schedules.php?employee_id=<?= $employee['id'] ?>" 
                                               class="btn btn-sm btn-info" title="Atur Jadwal">
                                                <i class="fas fa-clock"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Assignment Modal -->
<div class="modal fade" id="bulkAssignModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Assignment Jadwal Massal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" value="bulk_assign">
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Pilih karyawan dari tabel di bawah, lalu tentukan jadwal yang akan diterapkan.
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="bulk_work_schedule_id" class="form-label">Jadwal Kerja *</label>
                            <select class="form-select" id="bulk_work_schedule_id" name="work_schedule_id" required>
                                <option value="">Pilih Jadwal Kerja</option>
                                <?php foreach ($workSchedules as $ws): ?>
                                    <option value="<?= $ws['id'] ?>">
                                        <?= htmlspecialchars($ws['name']) ?> 
                                        (<?= formatTime($ws['start_time']) ?> - <?= formatTime($ws['end_time']) ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">Jadwal kerja harus dipilih</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="bulk_location_id" class="form-label">Lokasi Absen *</label>
                            <select class="form-select" id="bulk_location_id" name="location_id" required>
                                <option value="">Pilih Lokasi</option>
                                <?php foreach ($locations as $loc): ?>
                                    <option value="<?= $loc['id'] ?>">
                                        <?= htmlspecialchars($loc['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">Lokasi harus dipilih</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="bulk_start_date" class="form-label">Tanggal Mulai *</label>
                            <input type="date" class="form-control" id="bulk_start_date" name="start_date" 
                                   value="<?= date('Y-m-d') ?>" required>
                            <div class="invalid-feedback">Tanggal mulai harus diisi</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="bulk_end_date" class="form-label">Tanggal Selesai</label>
                            <input type="date" class="form-control" id="bulk_end_date" name="end_date">
                            <div class="form-text">Kosongkan jika tidak ada batas waktu</div>
                        </div>
                    </div>
                    
                    <div id="selectedEmployees" class="mt-3">
                        <h6>Karyawan Terpilih: <span id="selectedCount">0</span></h6>
                        <div id="selectedList" class="text-muted">Belum ada karyawan yang dipilih</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary" id="submitBulkAssign" disabled>
                        <i class="fas fa-save me-2"></i>Simpan Assignment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$additional_js = "
<script>
// Handle select all checkbox
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.employee-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateSelectedEmployees();
});

// Handle individual checkboxes
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('employee-checkbox')) {
        updateSelectedEmployees();
    }
});

function updateSelectedEmployees() {
    const checkboxes = document.querySelectorAll('.employee-checkbox:checked');
    const count = checkboxes.length;
    const submitBtn = document.getElementById('submitBulkAssign');
    
    document.getElementById('selectedCount').textContent = count;
    
    if (count > 0) {
        const names = Array.from(checkboxes).map(cb => {
            const row = cb.closest('tr');
            return row.cells[2].textContent.trim().split('\\n')[0];
        });
        
        document.getElementById('selectedList').innerHTML = names.join(', ');
        submitBtn.disabled = false;
        
        // Add hidden inputs for selected employees
        const form = submitBtn.closest('form');
        // Remove existing hidden inputs
        form.querySelectorAll('input[name=\"employee_ids[]\"]').forEach(input => input.remove());
        
        // Add new hidden inputs
        checkboxes.forEach(cb => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'employee_ids[]';
            input.value = cb.value;
            form.appendChild(input);
        });
    } else {
        document.getElementById('selectedList').textContent = 'Belum ada karyawan yang dipilih';
        submitBtn.disabled = true;
    }
}
</script>
";
?>

<?php include 'includes/footer.php'; ?>
