<?php
$page_title = 'Rolling Jadwal';
include 'includes/header.php';

// Handle form submissions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    if ($action == 'add') {
        $employee_a_id = (int)$_POST['employee_a_id'];
        $employee_b_id = (int)$_POST['employee_b_id'];
        $swap_date = $_POST['swap_date'];
        $notes = sanitizeInput($_POST['notes']);
        
        // Validation
        $errors = [];
        if ($employee_a_id <= 0) $errors[] = 'Karyawan A harus dipilih';
        if ($employee_b_id <= 0) $errors[] = 'Karyawan B harus dipilih';
        if ($employee_a_id == $employee_b_id) $errors[] = 'Karyawan A dan B tidak boleh sama';
        if (empty($swap_date)) $errors[] = 'Tanggal pertukaran harus diisi';
        if (strtotime($swap_date) < strtotime(date('Y-m-d'))) $errors[] = 'Tanggal pertukaran tidak boleh di masa lalu';
        
        if (empty($errors)) {
            try {
                // Get current schedules for both employees
                $schedule_a = $db->fetchOne("
                    SELECT es.work_schedule_id 
                    FROM employee_schedules es 
                    WHERE es.employee_id = ? AND es.is_active = 1 
                    AND (es.end_date IS NULL OR es.end_date >= ?)
                    ORDER BY es.start_date DESC LIMIT 1
                ", [$employee_a_id, $swap_date]);
                
                $schedule_b = $db->fetchOne("
                    SELECT es.work_schedule_id 
                    FROM employee_schedules es 
                    WHERE es.employee_id = ? AND es.is_active = 1 
                    AND (es.end_date IS NULL OR es.end_date >= ?)
                    ORDER BY es.start_date DESC LIMIT 1
                ", [$employee_b_id, $swap_date]);
                
                if (!$schedule_a || !$schedule_b) {
                    setAlert('danger', 'Salah satu atau kedua karyawan tidak memiliki jadwal aktif');
                } else {
                    // Check if swap already exists for this date
                    $existing = $db->fetchOne("
                        SELECT id FROM schedule_swaps 
                        WHERE swap_date = ? AND status != 'rejected'
                        AND ((employee_a_id = ? AND employee_b_id = ?) OR (employee_a_id = ? AND employee_b_id = ?))
                    ", [$swap_date, $employee_a_id, $employee_b_id, $employee_b_id, $employee_a_id]);
                    
                    if ($existing) {
                        setAlert('danger', 'Pertukaran jadwal untuk tanggal ini sudah ada');
                    } else {
                        $data = [
                            'employee_a_id' => $employee_a_id,
                            'employee_b_id' => $employee_b_id,
                            'swap_date' => $swap_date,
                            'original_schedule_a_id' => $schedule_a['work_schedule_id'],
                            'original_schedule_b_id' => $schedule_b['work_schedule_id'],
                            'requested_by' => $_SESSION['admin_id'],
                            'notes' => $notes
                        ];
                        
                        $db->insert('schedule_swaps', $data);
                        logActivity('ADD_SCHEDULE_SWAP', "Added schedule swap for date: {$swap_date}");
                        setAlert('success', 'Permintaan rolling jadwal berhasil ditambahkan');
                    }
                }
            } catch (Exception $e) {
                setAlert('danger', 'Terjadi kesalahan: ' . $e->getMessage());
            }
        } else {
            setAlert('danger', implode('<br>', $errors));
        }
        
        header('Location: schedule_swaps.php');
        exit();
    }
    
    if ($action == 'approve' || $action == 'reject') {
        $id = (int)$_POST['id'];
        $status = $action == 'approve' ? 'approved' : 'rejected';
        
        try {
            $data = [
                'status' => $status,
                'approved_by' => $_SESSION['admin_id'],
                'approval_date' => date('Y-m-d H:i:s')
            ];
            
            $db->update('schedule_swaps', $data, 'id = ?', [$id]);
            logActivity('UPDATE_SCHEDULE_SWAP', "Schedule swap {$status} for ID: {$id}");
            setAlert('success', 'Status rolling jadwal berhasil diperbarui');
        } catch (Exception $e) {
            setAlert('danger', 'Terjadi kesalahan: ' . $e->getMessage());
        }
        
        header('Location: schedule_swaps.php');
        exit();
    }
    
    if ($action == 'delete') {
        $id = (int)$_POST['id'];
        try {
            $swap = $db->fetchOne("SELECT * FROM schedule_swaps WHERE id = ?", [$id]);
            if ($swap && $swap['status'] == 'pending') {
                $db->delete('schedule_swaps', 'id = ?', [$id]);
                logActivity('DELETE_SCHEDULE_SWAP', "Deleted schedule swap for date: {$swap['swap_date']}");
                setAlert('success', 'Permintaan rolling jadwal berhasil dihapus');
            } else {
                setAlert('danger', 'Hanya permintaan dengan status pending yang dapat dihapus');
            }
        } catch (Exception $e) {
            setAlert('danger', 'Terjadi kesalahan: ' . $e->getMessage());
        }
        
        header('Location: schedule_swaps.php');
        exit();
    }
}

// Get schedule swaps data
try {
    $swaps = $db->fetchAll("
        SELECT ss.*, 
               ea.name as employee_a_name, ea.nik as employee_a_nik,
               eb.name as employee_b_name, eb.nik as employee_b_nik,
               wsa.name as schedule_a_name, wsa.start_time as schedule_a_start, wsa.end_time as schedule_a_end,
               wsb.name as schedule_b_name, wsb.start_time as schedule_b_start, wsb.end_time as schedule_b_end,
               ar.full_name as requested_by_name,
               aa.full_name as approved_by_name
        FROM schedule_swaps ss
        JOIN employees ea ON ss.employee_a_id = ea.id
        JOIN employees eb ON ss.employee_b_id = eb.id
        JOIN work_schedules wsa ON ss.original_schedule_a_id = wsa.id
        JOIN work_schedules wsb ON ss.original_schedule_b_id = wsb.id
        JOIN admin_users ar ON ss.requested_by = ar.id
        LEFT JOIN admin_users aa ON ss.approved_by = aa.id
        ORDER BY ss.request_date DESC
    ");
    
    // Get employees for form
    $employees = $db->fetchAll("
        SELECT e.id, e.name, e.nik, ws.name as schedule_name
        FROM employees e
        JOIN employee_schedules es ON e.id = es.employee_id
        JOIN work_schedules ws ON es.work_schedule_id = ws.id
        WHERE e.is_active = 1 AND es.is_active = 1
        AND (es.end_date IS NULL OR es.end_date >= CURDATE())
        ORDER BY e.name
    ");
    
} catch (Exception $e) {
    $error = "Error loading data: " . $e->getMessage();
}
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-exchange-alt me-2"></i>Rolling Jadwal
            </h1>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#swapModal" onclick="resetForm()">
                <i class="fas fa-plus me-2"></i>Tambah Rolling Jadwal
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card border-left-warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= count(array_filter($swaps, function($s) { return $s['status'] == 'pending'; })) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card border-left-success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Disetujui</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= count(array_filter($swaps, function($s) { return $s['status'] == 'approved'; })) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card border-left-danger">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Ditolak</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= count(array_filter($swaps, function($s) { return $s['status'] == 'rejected'; })) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-times fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <?php if (isset($error)): ?>
                    <div class="alert alert-danger"><?= $error ?></div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover data-table">
                            <thead>
                                <tr>
                                    <th>Tanggal Swap</th>
                                    <th>Karyawan A</th>
                                    <th>Karyawan B</th>
                                    <th>Jadwal Asal</th>
                                    <th>Status</th>
                                    <th>Diminta Oleh</th>
                                    <th>Tanggal Permintaan</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($swaps as $swap): ?>
                                    <tr>
                                        <td>
                                            <strong><?= formatDate($swap['swap_date']) ?></strong>
                                        </td>
                                        <td>
                                            <strong><?= htmlspecialchars($swap['employee_a_name']) ?></strong><br>
                                            <small class="text-muted">NIK: <?= htmlspecialchars($swap['employee_a_nik']) ?></small>
                                        </td>
                                        <td>
                                            <strong><?= htmlspecialchars($swap['employee_b_name']) ?></strong><br>
                                            <small class="text-muted">NIK: <?= htmlspecialchars($swap['employee_b_nik']) ?></small>
                                        </td>
                                        <td>
                                            <small>
                                                <strong>A:</strong> <?= htmlspecialchars($swap['schedule_a_name']) ?> 
                                                (<?= formatTime($swap['schedule_a_start']) ?>-<?= formatTime($swap['schedule_a_end']) ?>)<br>
                                                <strong>B:</strong> <?= htmlspecialchars($swap['schedule_b_name']) ?> 
                                                (<?= formatTime($swap['schedule_b_start']) ?>-<?= formatTime($swap['schedule_b_end']) ?>)
                                            </small>
                                        </td>
                                        <td>
                                            <?php
                                            $statusClass = [
                                                'pending' => 'warning',
                                                'approved' => 'success',
                                                'rejected' => 'danger'
                                            ];
                                            $statusText = [
                                                'pending' => 'Pending',
                                                'approved' => 'Disetujui',
                                                'rejected' => 'Ditolak'
                                            ];
                                            ?>
                                            <span class="badge bg-<?= $statusClass[$swap['status']] ?>">
                                                <?= $statusText[$swap['status']] ?>
                                            </span>
                                            <?php if ($swap['approved_by_name']): ?>
                                                <br><small class="text-muted">oleh: <?= htmlspecialchars($swap['approved_by_name']) ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?= htmlspecialchars($swap['requested_by_name']) ?>
                                        </td>
                                        <td>
                                            <?= formatDateTime($swap['request_date']) ?>
                                        </td>
                                        <td>
                                            <?php if ($swap['status'] == 'pending'): ?>
                                                <button type="button" class="btn btn-sm btn-success" 
                                                        onclick="updateStatus(<?= $swap['id'] ?>, 'approve')">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        onclick="updateStatus(<?= $swap['id'] ?>, 'reject')">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="deleteSwap(<?= $swap['id'] ?>)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Swap Modal -->
<div class="modal fade" id="swapModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Tambah Rolling Jadwal</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" value="add">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="employee_a_id" class="form-label">Karyawan A *</label>
                            <select class="form-select" id="employee_a_id" name="employee_a_id" required>
                                <option value="">Pilih Karyawan A</option>
                                <?php foreach ($employees as $emp): ?>
                                    <option value="<?= $emp['id'] ?>">
                                        <?= htmlspecialchars($emp['name']) ?> (<?= htmlspecialchars($emp['nik']) ?>) - <?= htmlspecialchars($emp['schedule_name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">Karyawan A harus dipilih</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="employee_b_id" class="form-label">Karyawan B *</label>
                            <select class="form-select" id="employee_b_id" name="employee_b_id" required>
                                <option value="">Pilih Karyawan B</option>
                                <?php foreach ($employees as $emp): ?>
                                    <option value="<?= $emp['id'] ?>">
                                        <?= htmlspecialchars($emp['name']) ?> (<?= htmlspecialchars($emp['nik']) ?>) - <?= htmlspecialchars($emp['schedule_name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">Karyawan B harus dipilih</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="swap_date" class="form-label">Tanggal Pertukaran *</label>
                            <input type="date" class="form-control" id="swap_date" name="swap_date" 
                                   min="<?= date('Y-m-d') ?>" required>
                            <div class="invalid-feedback">Tanggal pertukaran harus diisi</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">Catatan</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="Alasan atau catatan tambahan untuk pertukaran jadwal"></textarea>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Informasi:</strong> Sistem akan otomatis mengambil jadwal aktif dari masing-masing karyawan 
                        untuk tanggal yang dipilih.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$additional_js = "
<script>
function resetForm() {
    document.querySelector('#swapModal form').reset();
    document.querySelector('#swapModal form').classList.remove('was-validated');
}

function updateStatus(id, action) {
    const message = action === 'approve' ? 'menyetujui' : 'menolak';
    if (confirm('Apakah Anda yakin ingin ' + message + ' permintaan rolling jadwal ini?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"' + action + '\"><input type=\"hidden\" name=\"id\" value=\"' + id + '\">';
        document.body.appendChild(form);
        form.submit();
    }
}

function deleteSwap(id) {
    if (confirmDelete('Apakah Anda yakin ingin menghapus permintaan rolling jadwal ini?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"delete\"><input type=\"hidden\" name=\"id\" value=\"' + id + '\">';
        document.body.appendChild(form);
        form.submit();
    }
}

// Prevent selecting same employee for A and B
document.getElementById('employee_a_id').addEventListener('change', function() {
    const selectedValue = this.value;
    const employeeBSelect = document.getElementById('employee_b_id');
    
    // Reset employee B if same as A
    if (employeeBSelect.value === selectedValue) {
        employeeBSelect.value = '';
    }
});

document.getElementById('employee_b_id').addEventListener('change', function() {
    const selectedValue = this.value;
    const employeeASelect = document.getElementById('employee_a_id');
    
    // Reset employee A if same as B
    if (employeeASelect.value === selectedValue) {
        employeeASelect.value = '';
    }
});
</script>
";
?>

<?php include 'includes/footer.php'; ?>
