<?php
$page_title = 'Jam Kerja';
include 'includes/header.php';

// Handle form submissions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    if ($action == 'add' || $action == 'edit') {
        $name = sanitizeInput($_POST['name']);
        $start_time = $_POST['start_time'];
        $end_time = $_POST['end_time'];
        $early_check_in_limit = $_POST['early_check_in_limit'];
        $late_check_in_limit = $_POST['late_check_in_limit'];
        $early_check_out_limit = $_POST['early_check_out_limit'];
        $late_check_out_limit = $_POST['late_check_out_limit'];
        $is_cross_day = isset($_POST['is_cross_day']) ? 1 : 0;
        $description = sanitizeInput($_POST['description']);
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        
        // Validation
        $errors = [];
        if (empty($name)) $errors[] = 'Nama jam kerja harus diisi';
        if (empty($start_time)) $errors[] = 'Jam mulai harus diisi';
        if (empty($end_time)) $errors[] = 'Jam selesai harus diisi';
        if (empty($late_check_in_limit)) $errors[] = 'Batas terlambat absen masuk harus diisi';
        
        if (empty($errors)) {
            try {
                $data = [
                    'name' => $name,
                    'start_time' => $start_time,
                    'end_time' => $end_time,
                    'early_check_in_limit' => $early_check_in_limit ?: $start_time,
                    'late_check_in_limit' => $late_check_in_limit,
                    'early_check_out_limit' => $early_check_out_limit ?: $end_time,
                    'late_check_out_limit' => $late_check_out_limit ?: $end_time,
                    'is_cross_day' => $is_cross_day,
                    'description' => $description,
                    'is_active' => $is_active
                ];
                
                if ($action == 'add') {
                    $db->insert('work_schedules', $data);
                    logActivity('ADD_SCHEDULE', "Added work schedule: {$name}");
                    setAlert('success', 'Jam kerja berhasil ditambahkan');
                } else {
                    $id = (int)$_POST['id'];
                    $db->update('work_schedules', $data, 'id = ?', [$id]);
                    logActivity('EDIT_SCHEDULE', "Updated work schedule: {$name}");
                    setAlert('success', 'Jam kerja berhasil diperbarui');
                }
            } catch (Exception $e) {
                setAlert('danger', 'Terjadi kesalahan: ' . $e->getMessage());
            }
        } else {
            setAlert('danger', implode('<br>', $errors));
        }
        
        header('Location: schedules.php');
        exit();
    }
    
    if ($action == 'delete') {
        $id = (int)$_POST['id'];
        try {
            // Check if schedule is being used
            $usage = $db->fetchOne("SELECT COUNT(*) as total FROM employee_schedules WHERE work_schedule_id = ?", [$id]);
            if ($usage['total'] > 0) {
                setAlert('danger', 'Jam kerja tidak dapat dihapus karena sedang digunakan oleh karyawan');
            } else {
                $schedule = $db->fetchOne("SELECT name FROM work_schedules WHERE id = ?", [$id]);
                if ($schedule) {
                    $db->delete('work_schedules', 'id = ?', [$id]);
                    logActivity('DELETE_SCHEDULE', "Deleted work schedule: {$schedule['name']}");
                    setAlert('success', 'Jam kerja berhasil dihapus');
                }
            }
        } catch (Exception $e) {
            setAlert('danger', 'Terjadi kesalahan: ' . $e->getMessage());
        }
        
        header('Location: schedules.php');
        exit();
    }
}

// Get schedules data
try {
    $schedules = $db->fetchAll("
        SELECT ws.*, 
               (SELECT COUNT(*) FROM employee_schedules es WHERE es.work_schedule_id = ws.id AND es.is_active = 1) as employee_count
        FROM work_schedules ws
        ORDER BY ws.created_at DESC
    ");
} catch (Exception $e) {
    $error = "Error loading data: " . $e->getMessage();
}
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-clock me-2"></i>Jam Kerja
            </h1>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#scheduleModal" onclick="resetForm()">
                <i class="fas fa-plus me-2"></i>Tambah Jam Kerja
            </button>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <?php if (isset($error)): ?>
                    <div class="alert alert-danger"><?= $error ?></div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover data-table">
                            <thead>
                                <tr>
                                    <th>Nama</th>
                                    <th>Jam Kerja</th>
                                    <th>Batas Absen Masuk</th>
                                    <th>Batas Absen Keluar</th>
                                    <th>Lintas Hari</th>
                                    <th>Karyawan</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($schedules as $schedule): ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($schedule['name']) ?></strong>
                                            <?php if ($schedule['description']): ?>
                                                <br><small class="text-muted"><?= htmlspecialchars($schedule['description']) ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?= formatTime($schedule['start_time']) ?> - <?= formatTime($schedule['end_time']) ?>
                                        </td>
                                        <td>
                                            <?= formatTime($schedule['early_check_in_limit']) ?> - <?= formatTime($schedule['late_check_in_limit']) ?>
                                        </td>
                                        <td>
                                            <?= formatTime($schedule['early_check_out_limit']) ?> - <?= formatTime($schedule['late_check_out_limit']) ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $schedule['is_cross_day'] ? 'warning' : 'info' ?>">
                                                <?= $schedule['is_cross_day'] ? 'Ya' : 'Tidak' ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?= $schedule['employee_count'] ?> orang</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $schedule['is_active'] ? 'success' : 'danger' ?>">
                                                <?= $schedule['is_active'] ? 'Aktif' : 'Tidak Aktif' ?>
                                            </span>
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-warning" 
                                                    onclick="editSchedule(<?= htmlspecialchars(json_encode($schedule)) ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <?php if ($schedule['employee_count'] == 0): ?>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        onclick="deleteSchedule(<?= $schedule['id'] ?>, '<?= htmlspecialchars($schedule['name']) ?>')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            <?php else: ?>
                                                <button type="button" class="btn btn-sm btn-danger" disabled title="Tidak dapat dihapus karena sedang digunakan">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Schedule Modal -->
<div class="modal fade" id="scheduleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">Tambah Jam Kerja</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" id="formAction" value="add">
                    <input type="hidden" name="id" id="scheduleId">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Nama Jam Kerja *</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                            <div class="invalid-feedback">Nama jam kerja harus diisi</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" id="is_cross_day" name="is_cross_day">
                                <label class="form-check-label" for="is_cross_day">
                                    Shift Lintas Hari
                                </label>
                                <div class="form-text">Centang jika jam kerja melewati tengah malam</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="start_time" class="form-label">Jam Mulai *</label>
                            <input type="time" class="form-control" id="start_time" name="start_time" required>
                            <div class="invalid-feedback">Jam mulai harus diisi</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="end_time" class="form-label">Jam Selesai *</label>
                            <input type="time" class="form-control" id="end_time" name="end_time" required>
                            <div class="invalid-feedback">Jam selesai harus diisi</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="early_check_in_limit" class="form-label">Batas Awal Absen Masuk</label>
                            <input type="time" class="form-control" id="early_check_in_limit" name="early_check_in_limit">
                            <div class="form-text">Kosongkan untuk menggunakan jam mulai</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="late_check_in_limit" class="form-label">Batas Akhir Absen Masuk *</label>
                            <input type="time" class="form-control" id="late_check_in_limit" name="late_check_in_limit" required>
                            <div class="invalid-feedback">Batas akhir absen masuk harus diisi</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="early_check_out_limit" class="form-label">Batas Awal Absen Keluar</label>
                            <input type="time" class="form-control" id="early_check_out_limit" name="early_check_out_limit">
                            <div class="form-text">Kosongkan untuk menggunakan jam selesai</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="late_check_out_limit" class="form-label">Batas Akhir Absen Keluar</label>
                            <input type="time" class="form-control" id="late_check_out_limit" name="late_check_out_limit">
                            <div class="form-text">Kosongkan untuk menggunakan jam selesai</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="description" class="form-label">Deskripsi</label>
                            <textarea class="form-control" id="description" name="description" rows="2"></textarea>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                <label class="form-check-label" for="is_active">
                                    Status Aktif
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$additional_js = "
<script>
function resetForm() {
    document.getElementById('modalTitle').textContent = 'Tambah Jam Kerja';
    document.getElementById('formAction').value = 'add';
    document.getElementById('scheduleId').value = '';
    document.querySelector('#scheduleModal form').reset();
    document.querySelector('#scheduleModal form').classList.remove('was-validated');
    document.getElementById('is_active').checked = true;
}

function editSchedule(schedule) {
    document.getElementById('modalTitle').textContent = 'Edit Jam Kerja';
    document.getElementById('formAction').value = 'edit';
    document.getElementById('scheduleId').value = schedule.id;
    document.getElementById('name').value = schedule.name;
    document.getElementById('start_time').value = schedule.start_time;
    document.getElementById('end_time').value = schedule.end_time;
    document.getElementById('early_check_in_limit').value = schedule.early_check_in_limit || '';
    document.getElementById('late_check_in_limit').value = schedule.late_check_in_limit;
    document.getElementById('early_check_out_limit').value = schedule.early_check_out_limit || '';
    document.getElementById('late_check_out_limit').value = schedule.late_check_out_limit || '';
    document.getElementById('is_cross_day').checked = schedule.is_cross_day == 1;
    document.getElementById('description').value = schedule.description || '';
    document.getElementById('is_active').checked = schedule.is_active == 1;
    
    new bootstrap.Modal(document.getElementById('scheduleModal')).show();
}

function deleteSchedule(id, name) {
    if (confirmDelete('Apakah Anda yakin ingin menghapus jam kerja \"' + name + '\"?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"delete\"><input type=\"hidden\" name=\"id\" value=\"' + id + '\">';
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
";
?>

<?php include 'includes/footer.php'; ?>
