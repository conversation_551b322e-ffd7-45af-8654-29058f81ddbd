<?php
// Test database connection and basic functionality
require_once 'config/database.php';
require_once 'config/config.php';

echo "<h1>Test Koneksi Database dan <PERSON></h1>";

try {
    $db = new Database();
    echo "<p style='color: green;'>✓ Koneksi database berhasil</p>";
    
    // Test basic queries
    echo "<h2>Test Query Database</h2>";
    
    // Test departments
    $departments = $db->fetchAll("SELECT * FROM departments LIMIT 3");
    echo "<p>✓ Departments: " . count($departments) . " records</p>";
    
    // Test employees
    $employees = $db->fetchAll("SELECT * FROM employees LIMIT 3");
    echo "<p>✓ Employees: " . count($employees) . " records</p>";
    
    // Test work schedules
    $schedules = $db->fetchAll("SELECT * FROM work_schedules LIMIT 3");
    echo "<p>✓ Work Schedules: " . count($schedules) . " records</p>";
    
    // Test locations
    $locations = $db->fetchAll("SELECT * FROM locations LIMIT 3");
    echo "<p>✓ Locations: " . count($locations) . " records</p>";
    
    // Test employee schedules join
    echo "<h2>Test Join Query (Employee Schedules)</h2>";
    $employeeSchedules = $db->fetchAll("
        SELECT e.name as employee_name, ws.name as schedule_name, l.name as location_name
        FROM employees e
        LEFT JOIN employee_schedules es ON e.id = es.employee_id AND es.is_active = 1
        LEFT JOIN work_schedules ws ON es.work_schedule_id = ws.id
        LEFT JOIN locations l ON es.location_id = l.id
        LIMIT 5
    ");
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Employee</th><th>Schedule</th><th>Location</th></tr>";
    foreach ($employeeSchedules as $es) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($es['employee_name']) . "</td>";
        echo "<td>" . htmlspecialchars($es['schedule_name'] ?? 'No Schedule') . "</td>";
        echo "<td>" . htmlspecialchars($es['location_name'] ?? 'No Location') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test helper functions
    echo "<h2>Test Helper Functions</h2>";
    echo "<p>✓ formatDate: " . formatDate(date('Y-m-d')) . "</p>";
    echo "<p>✓ formatTime: " . formatTime('08:00:00') . "</p>";
    echo "<p>✓ formatDateTime: " . formatDateTime(date('Y-m-d H:i:s')) . "</p>";
    
    // Test session functions
    echo "<h2>Test Session Functions</h2>";
    if (!session_id()) {
        session_start();
    }
    
    setAlert('success', 'Test alert message');
    $alerts = getAlerts();
    if (!empty($alerts)) {
        echo "<p style='color: green;'>✓ Alert system working</p>";
    }
    
    echo "<h2>Database Tables Status</h2>";
    $tables = [
        'admin_users', 'employees', 'departments', 'positions', 
        'work_schedules', 'employee_schedules', 'locations', 
        'attendance', 'schedule_swaps', 'activity_logs'
    ];
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Table</th><th>Records</th><th>Status</th></tr>";
    
    foreach ($tables as $table) {
        try {
            $count = $db->fetchOne("SELECT COUNT(*) as count FROM {$table}")['count'];
            echo "<tr>";
            echo "<td>{$table}</td>";
            echo "<td>{$count}</td>";
            echo "<td style='color: green;'>✓ OK</td>";
            echo "</tr>";
        } catch (Exception $e) {
            echo "<tr>";
            echo "<td>{$table}</td>";
            echo "<td>-</td>";
            echo "<td style='color: red;'>✗ Error: " . $e->getMessage() . "</td>";
            echo "</tr>";
        }
    }
    echo "</table>";
    
    echo "<h2>File Structure Check</h2>";
    $files = [
        'index.php', 'login.php', 'employees.php', 'schedules.php',
        'locations.php', 'schedule_swaps.php', 'monitoring.php', 
        'reports.php', 'employee_schedules.php', 'schedule_assignments.php'
    ];
    
    echo "<ul>";
    foreach ($files as $file) {
        if (file_exists($file)) {
            echo "<li style='color: green;'>✓ {$file}</li>";
        } else {
            echo "<li style='color: red;'>✗ {$file} (missing)</li>";
        }
    }
    echo "</ul>";
    
    echo "<h2>API Files Check</h2>";
    $apiFiles = ['api/config.php', 'api/auth.php', 'api/attendance.php', 'api/schedule.php'];
    
    echo "<ul>";
    foreach ($apiFiles as $file) {
        if (file_exists($file)) {
            echo "<li style='color: green;'>✓ {$file}</li>";
        } else {
            echo "<li style='color: red;'>✗ {$file} (missing)</li>";
        }
    }
    echo "</ul>";
    
    echo "<h2>Configuration Check</h2>";
    echo "<p>✓ APP_NAME: " . APP_NAME . "</p>";
    echo "<p>✓ DB_HOST: " . DB_HOST . "</p>";
    echo "<p>✓ DB_NAME: " . DB_NAME . "</p>";
    echo "<p>✓ Timezone: " . date_default_timezone_get() . "</p>";
    echo "<p>✓ Current Time: " . date('Y-m-d H:i:s') . "</p>";
    
    echo "<hr>";
    echo "<h2 style='color: green;'>✓ Semua Test Berhasil!</h2>";
    echo "<p><strong>Aplikasi siap digunakan:</strong></p>";
    echo "<ul>";
    echo "<li><a href='login.php'>Login Admin</a> (username: admin, password: password)</li>";
    echo "<li><a href='employees.php'>Manajemen Karyawan</a></li>";
    echo "<li><a href='schedule_assignments.php'>Assignment Jadwal</a></li>";
    echo "<li><a href='monitoring.php'>Monitoring Absensi</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
    echo "<p>Pastikan:</p>";
    echo "<ul>";
    echo "<li>Database MySQL sudah berjalan</li>";
    echo "<li>Database 'absensipdam' sudah dibuat</li>";
    echo "<li>File database/absensipdam.sql sudah diimport</li>";
    echo "<li>Konfigurasi database di config/database.php sudah benar</li>";
    echo "</ul>";
}
?>
