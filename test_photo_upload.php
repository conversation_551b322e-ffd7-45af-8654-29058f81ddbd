<?php
/**
 * Test Photo Upload Functionality
 * File untuk testing upload foto absensi
 */

require_once 'config/database.php';
require_once 'config/config.php';

echo "<h1>Test Upload Foto Absensi</h1>";

// Handle form submission
if ($_POST && isset($_FILES['photo'])) {
    try {
        $employeeId = (int)$_POST['employee_id'];
        $type = $_POST['type']; // 'checkin' or 'checkout'
        
        if (!$employeeId) {
            throw new Exception('Employee ID required');
        }
        
        if (!in_array($type, ['checkin', 'checkout'])) {
            throw new Exception('Invalid type');
        }
        
        // Handle file upload
        $uploadDir = 'uploads/attendance_photos/';
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }
        
        $file = $_FILES['photo'];
        
        // Validate file
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('Upload error: ' . $file['error']);
        }
        
        // Validate file type
        $allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
        if (!in_array($file['type'], $allowedTypes)) {
            throw new Exception('Invalid file type. Only JPEG and PNG allowed.');
        }
        
        // Validate file size (5MB max)
        if ($file['size'] > 5 * 1024 * 1024) {
            throw new Exception('File too large. Maximum 5MB allowed.');
        }
        
        // Generate filename
        $timestamp = date('YmdHis');
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = "{$timestamp}_{$type}_emp{$employeeId}.{$extension}";
        $filepath = $uploadDir . $filename;
        
        // Move uploaded file
        if (!move_uploaded_file($file['tmp_name'], $filepath)) {
            throw new Exception('Failed to save file');
        }
        
        // Update database (simulate attendance record)
        $db = new Database();
        
        // Check if attendance record exists for today
        $today = date('Y-m-d');
        $attendance = $db->fetchOne(
            "SELECT * FROM attendance WHERE employee_id = ? AND attendance_date = ?",
            [$employeeId, $today]
        );
        
        if ($attendance) {
            // Update existing record
            $updateData = [];
            if ($type == 'checkin') {
                $updateData['check_in_photo'] = $filename;
                if (!$attendance['check_in_time']) {
                    $updateData['check_in_time'] = date('Y-m-d H:i:s');
                }
            } else {
                $updateData['check_out_photo'] = $filename;
                if (!$attendance['check_out_time']) {
                    $updateData['check_out_time'] = date('Y-m-d H:i:s');
                }
            }
            
            $db->update('attendance', $updateData, 'id = ?', [$attendance['id']]);
            $attendanceId = $attendance['id'];
        } else {
            // Create new record
            $attendanceData = [
                'employee_id' => $employeeId,
                'attendance_date' => $today,
                'status' => 'present'
            ];
            
            if ($type == 'checkin') {
                $attendanceData['check_in_photo'] = $filename;
                $attendanceData['check_in_time'] = date('Y-m-d H:i:s');
            } else {
                $attendanceData['check_out_photo'] = $filename;
                $attendanceData['check_out_time'] = date('Y-m-d H:i:s');
            }
            
            $attendanceId = $db->insert('attendance', $attendanceData);
        }
        
        echo "<div style='color: green; margin: 20px 0;'>";
        echo "<h3>✓ Upload Berhasil!</h3>";
        echo "<p><strong>File:</strong> {$filename}</p>";
        echo "<p><strong>Size:</strong> " . number_format($file['size'] / 1024, 2) . " KB</p>";
        echo "<p><strong>Type:</strong> {$file['type']}</p>";
        echo "<p><strong>Attendance ID:</strong> {$attendanceId}</p>";
        echo "<p><strong>Path:</strong> {$filepath}</p>";
        echo "</div>";
        
        // Display uploaded image
        echo "<div style='margin: 20px 0;'>";
        echo "<h4>Preview Foto:</h4>";
        echo "<img src='{$filepath}' alt='Uploaded Photo' style='max-width: 300px; border: 1px solid #ddd; border-radius: 5px;'>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='color: red; margin: 20px 0;'>";
        echo "<h3>✗ Upload Gagal!</h3>";
        echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
        echo "</div>";
    }
}

// Get employees for dropdown
try {
    $db = new Database();
    $employees = $db->fetchAll("SELECT id, nik, name FROM employees WHERE is_active = 1 ORDER BY name");
} catch (Exception $e) {
    echo "<p style='color: red;'>Error loading employees: " . $e->getMessage() . "</p>";
    $employees = [];
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.form-group { margin-bottom: 15px; }
label { display: block; margin-bottom: 5px; font-weight: bold; }
select, input[type="file"] { width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
button:hover { background: #0056b3; }
.info-box { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin: 20px 0; }
</style>

<div class="info-box">
    <h3>📸 Test Upload Foto Absensi</h3>
    <p>Gunakan form ini untuk testing upload foto absensi. Pilih karyawan, jenis absensi (masuk/pulang), dan upload foto.</p>
    <ul>
        <li><strong>Format:</strong> JPEG, PNG</li>
        <li><strong>Ukuran Max:</strong> 5 MB</li>
        <li><strong>Resolusi:</strong> Bebas (akan otomatis disesuaikan)</li>
    </ul>
</div>

<form method="POST" enctype="multipart/form-data">
    <div class="form-group">
        <label for="employee_id">Pilih Karyawan:</label>
        <select name="employee_id" id="employee_id" required>
            <option value="">-- Pilih Karyawan --</option>
            <?php foreach ($employees as $emp): ?>
                <option value="<?= $emp['id'] ?>">
                    <?= htmlspecialchars($emp['nik']) ?> - <?= htmlspecialchars($emp['name']) ?>
                </option>
            <?php endforeach; ?>
        </select>
    </div>
    
    <div class="form-group">
        <label for="type">Jenis Absensi:</label>
        <select name="type" id="type" required>
            <option value="">-- Pilih Jenis --</option>
            <option value="checkin">Absen Masuk</option>
            <option value="checkout">Absen Pulang</option>
        </select>
    </div>
    
    <div class="form-group">
        <label for="photo">Upload Foto:</label>
        <input type="file" name="photo" id="photo" accept="image/jpeg,image/png,image/jpg" required>
    </div>
    
    <button type="submit">Upload Foto</button>
</form>

<div class="info-box">
    <h3>📊 Test Results</h3>
    <p>Setelah upload berhasil:</p>
    <ol>
        <li>Cek halaman <a href="monitoring.php" target="_blank">Monitoring Absensi</a></li>
        <li>Lihat kolom "Foto" untuk karyawan yang dipilih</li>
        <li>Klik tombol "Lihat Foto" untuk melihat hasil upload</li>
        <li>Test juga detail view dengan klik tombol "Detail"</li>
    </ol>
</div>

<?php
// Display recent uploads
try {
    $recentUploads = $db->fetchAll("
        SELECT a.*, e.nik, e.name as employee_name,
               a.check_in_photo, a.check_out_photo
        FROM attendance a
        JOIN employees e ON a.employee_id = e.id
        WHERE (a.check_in_photo IS NOT NULL OR a.check_out_photo IS NOT NULL)
        ORDER BY a.created_at DESC
        LIMIT 10
    ");
    
    if (!empty($recentUploads)) {
        echo "<div class='info-box'>";
        echo "<h3>📷 Recent Photo Uploads</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>NIK</th>";
        echo "<th style='padding: 8px;'>Nama</th>";
        echo "<th style='padding: 8px;'>Tanggal</th>";
        echo "<th style='padding: 8px;'>Foto Masuk</th>";
        echo "<th style='padding: 8px;'>Foto Pulang</th>";
        echo "</tr>";
        
        foreach ($recentUploads as $upload) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($upload['nik']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($upload['employee_name']) . "</td>";
            echo "<td style='padding: 8px;'>" . $upload['attendance_date'] . "</td>";
            echo "<td style='padding: 8px;'>";
            if ($upload['check_in_photo']) {
                echo "<a href='uploads/attendance_photos/{$upload['check_in_photo']}' target='_blank'>";
                echo "<img src='uploads/attendance_photos/{$upload['check_in_photo']}' style='width: 50px; height: 50px; object-fit: cover; border-radius: 3px;'>";
                echo "</a>";
            } else {
                echo "-";
            }
            echo "</td>";
            echo "<td style='padding: 8px;'>";
            if ($upload['check_out_photo']) {
                echo "<a href='uploads/attendance_photos/{$upload['check_out_photo']}' target='_blank'>";
                echo "<img src='uploads/attendance_photos/{$upload['check_out_photo']}' style='width: 50px; height: 50px; object-fit: cover; border-radius: 3px;'>";
                echo "</a>";
            } else {
                echo "-";
            }
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error loading recent uploads: " . $e->getMessage() . "</p>";
}
?>

<div class="info-box">
    <h3>🔗 Quick Links</h3>
    <ul>
        <li><a href="monitoring.php">Monitoring Absensi</a> - Lihat foto di dashboard</li>
        <li><a href="test_connection.php">Test Connection</a> - Cek status sistem</li>
        <li><a href="employees.php">Data Karyawan</a> - Kelola data karyawan</li>
        <li><a href="api/README.md">API Documentation</a> - Dokumentasi API</li>
    </ul>
</div>
