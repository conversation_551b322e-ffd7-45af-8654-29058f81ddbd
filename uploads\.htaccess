# Security for uploads folder
# <PERSON>ya <PERSON> file gambar dan batasi eks<PERSON><PERSON>i script

# Disable script execution
Options -ExecCGI
AddHandler cgi-script .php .pl .py .jsp .asp .sh .cgi
Options -Indexes

# Only allow image files
<FilesMatch "\.(jpg|jpeg|png|gif|bmp|webp)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# Deny all other files
<FilesMatch "^(?!.*\.(jpg|jpeg|png|gif|bmp|webp)$).*$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Prevent access to .htaccess
<Files ".htaccess">
    Order Allow,Deny
    Deny from all
</Files>

# Set proper MIME types for images
<IfModule mod_mime.c>
    AddType image/jpeg .jpg .jpeg
    AddType image/png .png
    AddType image/gif .gif
    AddType image/bmp .bmp
    AddType image/webp .webp
</IfModule>

# Cache images for better performance
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/bmp "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
</IfModule>

# Compress images
<IfModule mod_deflate.c>
    <FilesMatch "\.(jpg|jpeg|png|gif|bmp|webp)$">
        SetOutputFilter DEFLATE
    </FilesMatch>
</IfModule>
